@forward 'element-plus/theme-chalk/src/common/var' with (
  // 基础色调
  $colors:
    (
      'primary': (
        'base': #007ee5,
      ),
      'success': (
        'base': #059669,
      ),
      'warning': (
        'base': #fbbf24,
      ),
      'danger': (
        'base': #f43f5e,
      ),
      'error': (
        'base': #f43f5e,
      ),
      'info': (
        'base': #909399,
      )
    ),
  $dialog: ('padding-primary': 54px 58px),
  $font-family: ('': 'Inter, Microsoft YaHei'),
  $overlay-color: ('lighter': rgba(0, 0, 0, 0.15)),
  $tabs: ('header-height': 60px)
);
@use 'element-plus/theme-chalk/src/index.scss' as *;
.el-dialog {
  border: 2px solid #e2e2e2;
  backdrop-filter: blur(22px);

  --el-dialog-margin-top: 100px;
  --el-dialog-bg-color: rgba(255, 255, 255, 0.74);
  --el-dialog-border-radius: 10px;
  --el-dialog-box-shadow: none;

  .el-dialog__header {
    padding-bottom: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-dialog__title {
      color: #19496a;
      font-size: 24px;
      font-weight: bold;
    }

    .el-dialog__headerbtn {
      border-radius: 50%;
      width: 20px;
      height: 20px;
      margin-top: -17px;
      background-color: #fff;
      position: static;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      // box-shadow: 0px 0 32px 4px rgba(0, 0, 0, 0);
      box-shadow: 0px 0 0px 0.5px var(--input-caret-color);
      transition: box-shadow 0.35s ease-in-out;
      &:hover {
        // box-shadow: 0px 0 32px 4px rgba(0, 0, 0, 0.1);
      }

      .el-icon {
        // color: #0d47a1;
        color: var(--input-caret-color);
        font-size: 12px;
      }
    }
  }

  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-button {
      display: none;
    }

    &::-webkit-scrollbar-corner {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgb(0, 126, 229, 0.3);
      border-radius: 5px;
      background-clip: content-box;
    }
  }

  .el-dialog__footer {
    margin-top: 28px;
    text-align: left;
  }
}

.el-dialog-common-cls {
  background: #fff;
  padding: 20px 12px 20px 24px;

  .el-dialog__header {
    padding-bottom: 20px;
    .el-dialog__title {
      color: var(--el-text-color-primary);
    }
  }
  
  .el-dialog__footer {
    text-align: center;
    margin-top: 0;
    .dialog-footer {
      display: flex;
      justify-content: center;
    }
  }
  
}

/* 按钮 */
.el-button {
  padding: 8px 24px;
  font-size: 16px;
  border-radius: 5px;
  height: auto;
  line-height: 1.3125;
  font-weight: normal;
  border-color: var(--el-border-color-lighter);
}

.el-button + .el-button {
  margin-left: 12px;
}

.el-input {
  --el-input-border-radius: 5px;
  --el-input-border-color: #d5d5d5;
  --el-input-height: 50px;
  --el-input-text-color: #161616;
  --el-input-icon-color: #161616;
  font-size: 18px;

  .el-input__wrapper {
    padding: 1px 20px;
  }
}

.el-select {
  --el-select-input-color: #18191a;

  .el-select__wrapper {
    box-shadow: 0 0 0 1px #d5d5d5 inset;
    border-radius: 5px;
    font-size: 18px;
    line-height: 50px;
    min-height: 50px;
  }
}

/* 下拉弹出框 脱离了页面 */
.el-popper {
  font-family: var(--font-family);
  font-size: var(--input-font-size);
  .el-select-dropdown__item {
    font-size: var(--input-font-size);
    font-weight: var(--input-font-weight);
    color: var(--input-text-color);
  }
  .el-select-dropdown__item.is-selected {
    font-weight: var(--input-font-weight);
    color: var(--theme-text-color);
  }
}

.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;

    .el-tabs__nav-wrap {
      .el-tabs__nav-scroll {
        .el-tabs__nav {
          .el-tabs__item {
            padding: 0 25px;
            font-size: 16px;
            color: #9a9a9a;
            font-weight: bold;

            display: flex;
            align-items: center;
            justify-content: center;

            &.is-active,
            &:hover {
              color: var(--el-color-primary);
            }

            .tabs-num {
              margin-left: 10px;
              border-radius: 10px;
              width: 26px;
              height: 26px;
              font-size: 14px;
              line-height: 1.35;
              background-color: #f5f5f5;
              color: #9a9a9a;
              font-weight: bold;

              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}

.el-table {
  --el-table-tr-bg-color: #fff;
  --el-table-header-bg-color: #f6fbff;
  --el-table-border: 1px solid #e0eaf2;

  .el-table__header-wrapper {
    border-radius: 5px 5px 0 0;
    border: 1px solid #e0eaf2;
    border-bottom: none;
  }

  .el-table__cell {
    padding: 6.5px 0;

    .cell {
      line-height: 1.35;
    }
  }
}

.el-pagination {
  .el-select {
    .el-select__wrapper {
      line-height: 38px;
      min-height: 38px;
    }
  }

  .el-pagination__editor {
    --el-input-inner-height: 38px;
    --el-input-height: 38px;
  }
}
<?php

namespace Modules\Editor\Models;

use Bingo\Base\BingoModel as Model;

/**
 * @property int $id 主键ID
 * @property string $name 模板名称
 * @property string $description 模板描述
 * @property string $thumbnail 缩略图URL
 * @property array $content 模板内容，JSON格式
 * @property int $category_id 分类ID
 * @property bool $is_system 是否系统模板
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */
class EditorTemplate extends Model
{
    protected $table = 'editor_templates';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'thumbnail',
        'content',
        'category_id',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'content' => 'json',
    ];
    
}

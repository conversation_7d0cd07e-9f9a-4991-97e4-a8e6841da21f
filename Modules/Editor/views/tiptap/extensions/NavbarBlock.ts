import { mergeAttributes, Node, type Command } from '@tiptap/core'
import { navbarTemplate } from '../templates/navbar.template'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    navbarBlock: {
      insertNavbarBlock: () => ReturnType
    }
  }
}

export const NavbarBlock = Node.create({
  name: 'navbarBlock',
  
  group: 'block',
  
  draggable: true,
  
  isolating: true,
  
  atom: true, // 将节点设置为原子节点，防止内部内容被编辑
  
  parseHTML() {
    return [
      {
        tag: 'nav[data-bs-component="navbar"]',
        getAttrs: element => {
          if (!(element instanceof HTMLElement)) {
            return false
          }
          
          return {
            class: element.getAttribute('class') || 'navbar-section responsive-block',
            contentHTML: element.innerHTML,
          }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes, node }) {
    // 确保Bootstrap样式已加载（如果需要）
    const bootstrapStyle = document.querySelector('link[href*="bootstrap"]');
    if (!bootstrapStyle) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css';
      document.head.appendChild(link);
    }
    
    // 合并属性，确保data-bs-component="navbar"属性始终存在
    const finalAttributes = mergeAttributes(
      {
        'data-bs-component': 'navbar',
        'class': HTMLAttributes.class || 'navbar-section responsive-block'
      },
      HTMLAttributes
    )
    
    // 确保data-bs-component不被其他属性覆盖
    finalAttributes['data-bs-component'] = 'navbar'
    
    // 处理内容HTML
    let contentHTML = '';
    if (HTMLAttributes.contentHTML) {
      contentHTML = HTMLAttributes.contentHTML;
    } else {
      // 如果没有保存的HTML内容，从模板中提取内容
      const tempEl = document.createElement('div');
      tempEl.innerHTML = navbarTemplate;
      contentHTML = tempEl.firstElementChild?.innerHTML || '';
    }
    
    // 创建最终的HTML结构
    const container = document.createElement('nav');
    Object.entries(finalAttributes).forEach(([key, value]) => {
      if (key !== 'contentHTML') {
        container.setAttribute(key, value.toString());
      }
    });
    container.innerHTML = contentHTML;
    
    // 确保导航栏的交互功能在插入后正常工作
    setTimeout(() => {
      // 查找并处理所有导航栏折叠按钮
      const navbarTogglers = document.querySelectorAll('.navbar-section .navbar-toggler');
      navbarTogglers.forEach(toggler => {
        if (!(toggler instanceof HTMLElement)) return;
        
        toggler.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          
          const targetId = toggler.getAttribute('data-bs-target');
          if (!targetId) return;
          
          const targetCollapse = document.querySelector(targetId);
          if (!targetCollapse) return;
          
          if (targetCollapse.classList.contains('show')) {
            targetCollapse.classList.remove('show');
          } else {
            targetCollapse.classList.add('show');
          }
        });
      });
    }, 100);
    
    // 使用outerHTML将完整的HTML结构传回给编辑器
    return {
      dom: container,
      contentDOM: null, // 使用null表示这是一个原子节点
    }
  },

  addAttributes() {
    return {
      style: {
        default: null,
        parseHTML: element => element.getAttribute('style'),
        renderHTML: attributes => {
          if (!attributes.style) {
            return {}
          }
          
          return {
            style: attributes.style
          }
        }
      },
      class: {
        default: 'navbar-section responsive-block',
        parseHTML: element => element.getAttribute('class'),
        renderHTML: attributes => {
          if (!attributes.class) {
            return { class: 'navbar-section responsive-block' }
          }
          
          return {
            class: attributes.class
          }
        }
      },
      contentHTML: {
        default: null,
        parseHTML: element => element.innerHTML,
        renderHTML: attributes => {
          return { contentHTML: attributes.contentHTML }
        }
      }
    }
  },

  addCommands() {
    return {
      insertNavbarBlock:
        () =>
        ({ commands }) => {
          return commands.insertContent(navbarTemplate)
        },
    }
  },
  
  // 添加钩子确保每次更新后导航栏交互功能正常
  onUpdate() {
    setTimeout(() => {
      const navbarTogglers = document.querySelectorAll('.navbar-section .navbar-toggler');
      navbarTogglers.forEach(toggler => {
        if (!(toggler instanceof HTMLElement)) return;
        
        // 移除所有现有事件监听器，避免重复绑定
        const newToggler = toggler.cloneNode(true) as HTMLElement;
        if (toggler.parentNode) {
          toggler.parentNode.replaceChild(newToggler, toggler);
        }
        
        // 添加新的事件监听器
        newToggler.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          
          const targetId = newToggler.getAttribute('data-bs-target');
          if (!targetId) return;
          
          const targetCollapse = document.querySelector(targetId);
          if (!targetCollapse) return;
          
          if (targetCollapse.classList.contains('show')) {
            targetCollapse.classList.remove('show');
          } else {
            targetCollapse.classList.add('show');
          }
        });
      });
    }, 100);
  }
}) 
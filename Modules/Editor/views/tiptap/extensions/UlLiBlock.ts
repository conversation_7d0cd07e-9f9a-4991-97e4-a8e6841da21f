import { Node, mergeAttributes } from '@tiptap/core'

export interface UlLiBlockOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    ulLiBlock: {
      /**
       * 设置无序列表
       */
      setUnorderedList: () => ReturnType
      /**
       * 切换无序列表
       */
      toggleUnorderedList: () => ReturnType
    }
  }
}

export const UlLiBlock = Node.create<UlLiBlockOptions>({
  name: 'ulLiBlock',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  group: 'block',

  content: 'listItem+',

  parseHTML() {
    return [
      { tag: 'ul' },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },

  addCommands() {
    return {
      setUnorderedList: () => ({ commands }) => {
        return commands.wrapIn(this.name)
      },
      toggleUnorderedList: () => ({ commands }) => {
        return commands.toggleList(this.name, 'listItem')
      },
    }
  },
})

export const ListItem = Node.create({
  name: 'listItem',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  content: 'paragraph block*',

  defining: true,

  parseHTML() {
    return [
      { tag: 'li' },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },
}) 
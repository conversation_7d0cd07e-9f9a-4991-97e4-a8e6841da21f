<template>
  <div class="edit-section">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="内容" name="content">
        <div class="editor-container">
          <div class="editor-toolbar">
            <el-button-group>
              <el-button 
                :class="{ 'is-active': editor?.isActive('bold') }"
                @click="editor?.chain().focus().toggleBold().run()"
              >
                加粗
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive('italic') }"
                @click="editor?.chain().focus().toggleItalic().run()"
              >
                斜体
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive('strike') }"
                @click="editor?.chain().focus().toggleStrike().run()"
              >
                删除线
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button 
                v-for="level in headingLevels" 
                :key="level"
                :class="{ 'is-active': editor?.isActive('heading', { level }) }"
                @click="editor?.chain().focus().toggleHeading({ level }).run()"
              >
                标题{{ level }}
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button 
                :class="{ 'is-active': editor?.isActive('bulletList') }"
                @click="editor?.chain().focus().toggleBulletList().run()"
              >
                无序列表
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive('orderedList') }"
                @click="editor?.chain().focus().toggleOrderedList().run()"
              >
                有序列表
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button 
                :class="{ 'is-active': editor?.isActive({ textAlign: 'left' }) }"
                @click="editor?.chain().focus().setTextAlign('left').run()"
              >
                左对齐
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive({ textAlign: 'center' }) }"
                @click="editor?.chain().focus().setTextAlign('center').run()"
              >
                居中
              </el-button>
              <el-button 
                :class="{ 'is-active': editor?.isActive({ textAlign: 'right' }) }"
                @click="editor?.chain().focus().setTextAlign('right').run()"
              >
                右对齐
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-button-group>
              <el-button @click="editor?.chain().focus().undo().run()">
                撤销
              </el-button>
              <el-button @click="editor?.chain().focus().redo().run()">
                重做
              </el-button>
            </el-button-group>

            <el-divider direction="vertical" />

            <el-color-picker 
              v-model="currentTextColor"
              :predefine="predefineColors"
              size="small"
              @change="onTextColorChange"
            />
          </div>
          <editor-content :editor="editor" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="样式" name="style">
        <el-form label-position="top" size="small">
          <el-form-item label="文本颜色">
            <el-color-picker v-model="textColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="背景颜色">
            <el-color-picker v-model="backgroundColor" @change="markAsChanged" />
          </el-form-item>

          <el-form-item label="内边距">
            <div class="padding-inputs">
              <div class="padding-input">
                <span>上</span>
                <el-input-number 
                  v-model="padding.top" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
              <div class="padding-input">
                <span>右</span>
                <el-input-number 
                  v-model="padding.right" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
              <div class="padding-input">
                <span>下</span>
                <el-input-number 
                  v-model="padding.bottom" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
              <div class="padding-input">
                <span>左</span>
                <el-input-number 
                  v-model="padding.left" 
                  :min="0" 
                  :max="100"
                  @change="markAsChanged"
                />
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 应用按钮，只在有更改时显示 -->
    <div v-if="isChanged" class="apply-button-container">
      <el-button type="primary" @click="applyChanges" size="small">应用更改</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineProps, defineEmits, defineOptions, watch, nextTick } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import { Color } from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import { ElMessage } from 'element-plus'

// 定义组件名称
defineOptions({
  name: 'RichTextEditor'
})

const props = defineProps({
  blockElement: {
    type: Object as () => HTMLElement | null,
    default: null
  }
})

const emit = defineEmits(['update-block'])

const activeTab = ref('content')
const editor = ref<Editor | null>(null)
const textColor = ref('#000000')
const backgroundColor = ref('#ffffff')
const isChanged = ref(false)
const padding = ref({
  top: 0,
  right: 0,
  bottom: 0,
  left: 0
})

// 原始HTML和结构信息
const originalHtml = ref('')
const originalStructure = ref({
  containerClasses: [] as string[],
  rowClasses: [] as string[],
  colClasses: [] as string[],
  richTextClasses: [] as string[],
  attributes: {} as Record<string, string>
})

// 标题级别
const headingLevels = [1, 2, 3] as const

// 预定义颜色
const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#000000',
  '#666666',
  '#999999'
]

// 当前文本颜色
const currentTextColor = ref('#000000')

// 标记为已更改
const markAsChanged = () => {
  isChanged.value = true
}

/**
 * 提取富文本内容和样式
 */
const extractRichTextContent = () => {
  if (!props.blockElement) return false
  
  try {
    // 确保在提取新内容前状态是清空的
    originalHtml.value = props.blockElement.outerHTML
    
    // 清空原始结构
    originalStructure.value = {
      containerClasses: [],
      rowClasses: [],
      colClasses: [],
      richTextClasses: [],
      attributes: {}
    }
    
    // 解析DOM结构，提取原始类和属性
    // 1. 找到主容器
    const container = props.blockElement.querySelector('.container, .container-fluid')
    if (container) {
      originalStructure.value.containerClasses = Array.from(container.classList)
      
      // 2. 找到行元素
      const row = container.querySelector('.row')
      if (row) {
        originalStructure.value.rowClasses = Array.from(row.classList)
        
        // 3. 找到列元素
        const col = row.querySelector('[class*="col-"]')
        if (col) {
          originalStructure.value.colClasses = Array.from(col.classList)
        }
      }
    }
    
    // 4. 找到富文本内容元素 - 可能在不同位置和标签内
    let contentElement = null
    
    // 首先尝试找到指定类名的元素
    contentElement = props.blockElement.querySelector('.rich-text-content')
    
    // 如果没找到，尝试找到rich-text组件内的内容区域
    if (!contentElement) {
      // 首先尝试查找已有的data-bs-component="rich-text"属性
      const richTextComponent = props.blockElement.hasAttribute('data-bs-component') && 
                                props.blockElement.getAttribute('data-bs-component') === 'rich-text' ? 
                                props.blockElement : 
                                props.blockElement.querySelector('[data-bs-component="rich-text"]')
      
      if (richTextComponent) {
        // 保存富文本组件的类名
        originalStructure.value.richTextClasses = Array.from(richTextComponent.classList)
        
        // 保存自定义属性
        Array.from(richTextComponent.attributes).forEach(attr => {
          if (attr.name !== 'class' && attr.name !== 'style') {
            originalStructure.value.attributes[attr.name] = attr.value
          }
        })
        
        // 如果是h1-h6、p等标签，直接使用其内容
        if (/^(h[1-6]|p|div)$/i.test(richTextComponent.tagName)) {
          contentElement = richTextComponent
        } else {
          // 否则，寻找其中的文本容器
          // 尝试查找第一个有意义的内容容器
          const potentialContentElements = richTextComponent.querySelectorAll('h1, h2, h3, h4, h5, h6, p, div.text-content, div.content')
          
          if (potentialContentElements.length > 0) {
            // 使用第一个元素作为内容元素
            contentElement = potentialContentElements[0].parentElement
          } else {
            // 如果都找不到，使用富文本组件本身
            contentElement = richTextComponent
          }
        }
      }
    }
    
    // 如果仍然没找到，尝试查找任何可能含有文本的容器
    if (!contentElement) {
      const col = props.blockElement.querySelector('[class*="col-"]')
      if (col) {
        contentElement = col
      } else {
        contentElement = props.blockElement
      }
    }
    
    if (contentElement) {
      // 提取内容和样式
      const content = contentElement.innerHTML
      const computedStyle = window.getComputedStyle(contentElement)
      
      // 设置样式属性
      textColor.value = computedStyle.color
      backgroundColor.value = computedStyle.backgroundColor
      padding.value = {
        top: parseInt(computedStyle.paddingTop) || 0,
        right: parseInt(computedStyle.paddingRight) || 0,
        bottom: parseInt(computedStyle.paddingBottom) || 0,
        left: parseInt(computedStyle.paddingLeft) || 0
      }
      
      // 初始化编辑器
      const editorInstance = new Editor({
        extensions: [
          StarterKit,
          TextAlign.configure({
            types: ['heading', 'paragraph']
          }),
          TextStyle,
          Color
        ],
        content: content,
        onUpdate: () => {
          markAsChanged()
        }
      })
      
      editor.value = editorInstance
      return true
    }
    
    return false
  } catch (error) {
    console.error('提取富文本内容时出错:', error)
    return false
  }
}

// 初始化编辑器的函数
const initEditor = (content: string = '<p></p>') => {
  // 确保先清理现有编辑器
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }

  // 等待下一个 tick 再创建新编辑器
  nextTick(() => {
    editor.value = new Editor({
      extensions: [
        StarterKit,
        TextAlign.configure({
          types: ['heading', 'paragraph']
        }),
        TextStyle,
        Color
      ],
      content: content,
      onUpdate: () => {
        markAsChanged()
      }
    })
  })
}

// 文本颜色改变处理函数
const onTextColorChange = (color: string) => {
  if (editor.value) {
    editor.value.chain().focus().setColor(color).run()
    markAsChanged()
  }
}

// 清空编辑器内容
const clearEditor = () => {
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }
  // 重置所有状态
  isChanged.value = false
  currentTextColor.value = '#000000'
  originalHtml.value = ''
  originalStructure.value = {
    containerClasses: [],
    rowClasses: [],
    colClasses: [],
    richTextClasses: [],
    attributes: {}
  }
  padding.value = {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }
}

// 监听 blockElement 的变化
watch(() => props.blockElement, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 1. 先清空当前编辑器和状态
    clearEditor()
    
    // 2. 等待 DOM 更新
    nextTick(() => {
      if (newValue) {
        // 3. 提取新内容并初始化编辑器
        const extracted = extractRichTextContent()
        if (!extracted) {
          // 如果无法提取内容，使用空内容初始化编辑器
          initEditor('<p></p>')
        }
      } else {
        // 如果 blockElement 为空，初始化空编辑器
        initEditor('<p></p>')
      }
    })
  }
}, { immediate: true, deep: true })

// 修改 onMounted 钩子
onMounted(() => {
  // 初始化时标记为未更改
  isChanged.value = false
})

// 生成富文本HTML
const generateRichTextHTML = (): string => {
  if (!editor.value) {
    return ''
  }

  // 获取编辑器内容
  const editorContent = editor.value.getHTML()
  
  // 应用样式
  const style = [
    `color: ${textColor.value}`,
    `background-color: ${backgroundColor.value}`,
    `padding: ${padding.value.top}px ${padding.value.right}px ${padding.value.bottom}px ${padding.value.left}px`
  ].join(';')

  // 检查原始元素是否为单个标签（p, h1-h6）
  const isSingleTag = props.blockElement && (
    // 检查DOM元素标签名
    /^(H[1-6]|P)$/i.test(props.blockElement.tagName) ||
    // 检查HTML字符串是否以标签开头和结尾
    /^<(h[1-6]|p)[\s>].*<\/(h[1-6]|p)>$/i.test(props.blockElement.outerHTML.trim())
  );
    
  // 检查原始HTML是否为纯文本容器
  const isDirectRichTextComponent = props.blockElement && 
    props.blockElement.hasAttribute('data-bs-component') && 
    props.blockElement.getAttribute('data-bs-component') === 'rich-text' &&
    !props.blockElement.querySelector('.container, .container-fluid, .row, [class*="col-"]');
  
  // 如果是单个标签或直接的富文本组件
  if (isSingleTag || isDirectRichTextComponent) {
    // 构建自定义属性字符串
    let attributesStr = ''
    Object.entries(originalStructure.value.attributes).forEach(([key, value]) => {
      if (key !== 'class' && key !== 'style') {
        attributesStr += ` ${key}="${value}"`
      }
    })
    
    // 确保有data-bs-component属性
    if (!attributesStr.includes('data-bs-component')) {
      attributesStr += ' data-bs-component="rich-text"'
    }
    
    // 获取原始元素的类名
    const originalClasses = props.blockElement ? Array.from(props.blockElement.classList) : []
    const classesStr = originalClasses.length > 0 ? ` class="${originalClasses.join(' ')}"` : ''
    
    // 解析编辑器内容
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = editorContent
    
    // 检查编辑器内容是否为单个标签
    if (tempDiv.children.length === 1 && /^(H[1-6]|P)$/i.test(tempDiv.children[0].tagName)) {
      const contentElement = tempDiv.children[0]
      const tagName = contentElement.tagName.toLowerCase()
      
      // 如果原始元素也是相同类型的标签，直接使用新内容更新
      if (props.blockElement && props.blockElement.tagName.toLowerCase() === tagName) {
        return `<${tagName}${classesStr}${attributesStr} style="${style}">${contentElement.innerHTML}</${tagName}>`
      }
      
      // 如果不同类型，但都是标题或段落，也直接返回内容元素
      return `<${tagName}${classesStr}${attributesStr} style="${style}">${contentElement.innerHTML}</${tagName}>`
    } else {
      // 编辑器内容不是单个标签，但原始元素是
      if (isSingleTag && props.blockElement) {
        const tagName = props.blockElement.tagName.toLowerCase()
        return `<${tagName}${classesStr}${attributesStr} style="${style}">${editorContent}</${tagName}>`
      }
      
      // 当原始元素是直接的富文本组件时
      return `<div${classesStr}${attributesStr} style="${style}">${editorContent}</div>`
    }
  }

  // 编辑器内容是单个标签，但原始容器不是
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = editorContent
  
  if (tempDiv.children.length === 1 && /^(H[1-6]|P)$/i.test(tempDiv.children[0].tagName)) {
    const contentElement = tempDiv.children[0]
    const contentHtml = contentElement.innerHTML
    const tagName = contentElement.tagName.toLowerCase()
    
    // 检查原始块是否包含带有data-bs-component="rich-text"的元素
    const richTextEl = props.blockElement?.querySelector('[data-bs-component="rich-text"]')
    
    if (richTextEl && /^(H[1-6]|P)$/i.test(richTextEl.tagName)) {
      // 如果原始富文本元素也是标题或段落，直接更新它
      const classes = Array.from(richTextEl.classList).join(' ')
      const classAttr = classes ? ` class="${classes}"` : ''
      
      let attributesStr = ''
      Array.from(richTextEl.attributes).forEach(attr => {
        if (attr.name !== 'class' && attr.name !== 'style') {
          attributesStr += ` ${attr.name}="${attr.value}"`
        }
      })
      
      // 确保有data-bs-component属性
      if (!attributesStr.includes('data-bs-component')) {
        attributesStr += ' data-bs-component="rich-text"'
      }
      
      return `<${tagName}${classAttr}${attributesStr} style="${style}">${contentHtml}</${tagName}>`
    }
  }

  // 检查原始HTML是否在富文本组件内
  const isInRichTextComponent = props.blockElement?.querySelector('[data-bs-component="rich-text"]') ||
    (props.blockElement?.getAttribute('data-bs-component') === 'rich-text')
    
  // 如果是完整的营销模板结构中的富文本组件
  if (isInRichTextComponent) {
    // 克隆原始HTML结构
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = originalHtml.value
    
    // 寻找富文本内容容器
    const richTextComponent = tempDiv.querySelector('[data-bs-component="rich-text"]') || tempDiv
    
    // 如果找到了富文本容器
    if (richTextComponent) {
      // 查找实际的内容元素
      const contentElement = richTextComponent.querySelector('h1, h2, h3, h4, h5, h6, p, div') || richTextComponent
      
      // 替换内容元素的内容
      contentElement.innerHTML = editorContent
      
      // 应用样式到内容元素
      contentElement.setAttribute('style', style)
      
      return tempDiv.innerHTML
    }
  }
  
  // 保留原始结构或使用默认结构
  const containerClasses = originalStructure.value.containerClasses.length > 0 ? 
    originalStructure.value.containerClasses.join(' ') : 
    'container py-5'
  
  const rowClasses = originalStructure.value.rowClasses.length > 0 ?
    originalStructure.value.rowClasses.join(' ') :
    'row justify-content-center'
  
  const colClasses = originalStructure.value.colClasses.length > 0 ?
    originalStructure.value.colClasses.join(' ') :
    'col-12 col-md-10 col-lg-8'
  
  const richTextClasses = originalStructure.value.richTextClasses.length > 0 ?
    originalStructure.value.richTextClasses.filter(cls => cls !== 'rich-text-content').join(' ') :
    ''
  
  // 构建自定义属性字符串
  let attributesStr = ''
  Object.entries(originalStructure.value.attributes).forEach(([key, value]) => {
    attributesStr += ` ${key}="${value}"`
  })
  
  // 确保有data-bs-component属性
  if (!attributesStr.includes('data-bs-component')) {
    attributesStr += ' data-bs-component="rich-text"'
  }

  // 默认标准结构
  return `
    <div class="${containerClasses}">
      <div class="${rowClasses}">
        <div class="${colClasses}">
          <div class="rich-text-content ${richTextClasses}"${attributesStr} style="${style}">
            ${editorContent}
          </div>
        </div>
      </div>
    </div>
  `.trim()
}

// 应用更改
const applyChanges = () => {
  try {
    const html = generateRichTextHTML()
    
    // 发出更新事件
    emit('update-block', { html })
    
    // 重置更改状态
    isChanged.value = false
    
    ElMessage.success('富文本内容已更新')
  } catch (error) {
    console.error('应用富文本更改时出错:', error)
    ElMessage.error('更新富文本失败')
  }
}

// 组件销毁时清理编辑器
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
    editor.value = null
  }
})
</script>

<style lang="scss" scoped>
.edit-section {
  margin-bottom: 15px;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

:deep(.el-button) {
  padding: 6px 12px;
  font-size: 13px;
  
  &.is-active {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #409eff;
  }

  &:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
}

:deep(.el-color-picker) {
  margin-left: 5px;
}

:deep(.ProseMirror) {
  padding: 20px;
  min-height: 200px;
  outline: none;
  background-color: #ffffff;

  p {
    margin: 1em 0;
    line-height: 1.6;
  }

  h1, h2, h3 {
    margin: 1.5em 0 0.5em;
    line-height: 1.4;
  }

  ul, ol {
    padding-left: 1.5em;
    margin: 1em 0;
  }

  li {
    margin: 0.5em 0;
  }
}

.padding-inputs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.padding-input {
  display: flex;
  flex-direction: column;
  gap: 4px;

  span {
    font-size: 12px;
    color: #909399;
  }
}

.apply-button-container {
  margin-top: 15px;
  text-align: center;
  padding: 8px 0;
  border-top: 1px dashed #e4e7ed;
}
</style> 
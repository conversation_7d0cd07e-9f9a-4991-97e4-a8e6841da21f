<?php

namespace Modules\Editor\Admin\Controllers;

use Bingo\Base\BingoController;
use Illuminate\Http\Request;
use Modules\Editor\Services\EditorTemplateService;

class EditorTemplateController extends BingoController
{
    public function __construct(
        protected readonly EditorTemplateService $templateService
    ) {
    }

    /**
     * 获取模板列表
     *
     * @return array
     */
    public function index(): array
    {
        return $this->templateService->getAllTemplates();
    }

    /**
     * 创建模板
     *
     * @param Request $request
     * @return array
     */
    public function store(Request $request): array
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'thumbnail' => 'nullable|string|max:255',
            'content' => 'required|array',
        ]);

        return $this->templateService->createTemplate($validated);
    }

    /**
     * 获取模板详情
     *
     * @param int $id
     * @return array
     */
    public function show(int $id): array
    {
        return $this->templateService->getTemplateById($id);
    }

    /**
     * 更新模板
     *
     * @param Request $request
     * @param int $id
     * @return array
     */
    public function update(Request $request, int $id): array
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'thumbnail' => 'nullable|string|max:255',
            'content' => 'nullable|array',
            'category_id' => 'nullable|integer|exists:editor_categories,id',
            'is_system' => 'nullable|boolean',
        ]);

        return $this->templateService->updateTemplate($id, $validated);
    }

    /**
     * 删除模板
     *
     * @param int $id
     * @return array
     */
    public function destroy(int $id): array
    {
        $result = $this->templateService->deleteTemplate($id);
        return ['success' => $result];
    }

    /**
     * 根据分类获取模板
     *
     * @param int $categoryId
     * @return array
     */
    public function getByCategory(int $categoryId): array
    {
        return $this->templateService->getTemplatesByCategory($categoryId);
    }

    /**
     * 获取系统模板
     *
     * @return array
     */
    public function getSystemTemplates(): array
    {
        return $this->templateService->getSystemTemplates();
    }

    /**
     * 获取用户模板
     *
     * @return array
     */
    public function getUserTemplates(): array
    {
        return $this->templateService->getUserTemplates();
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('editor_templates', function (Blueprint $table) {
            $table->comment('可视化编辑器模板表');
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('name')->comment('模板名称');
            $table->string('thumbnail')->nullable()->comment('缩略图URL');
            $table->json('content')->comment('模板内容，JSON格式');
            $table->unsignedBigInteger('category_id')->nullable()->comment('分类ID');
            
            $table->unsignedInteger('creator_id')->default(0)->comment('创建者ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->comment('删除时间');
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('editor_templates');
    }
};

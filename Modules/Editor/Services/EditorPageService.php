<?php

namespace Modules\Editor\Services;

use Bingo\Exceptions\BizException;
use Modules\Editor\Models\EditorPage;
use Mo<PERSON>les\Editor\Models\EditorCategory;
use Illuminate\Support\Str;

class EditorPageService
{
    /**
     * 获取所有页面
     *
     * @return array
     */
    public function getAllPages(): array
    {
        return EditorPage::get()->toArray();
    }

    /**
     * 获取页面详情
     *
     * @param int $id
     * @return array
     */
    public function getPageById(int $id): array
    {
        $page = EditorPage::find($id);
        if (!$page) {
            throw new BizException('页面不存在');
        }
        return $page->toArray();
    }

    /**
     * 根据slug获取页面
     *
     * @param string $slug
     * @return array
     */
    public function getPageBySlug(string $slug): array
    {
        $page = EditorPage::where('slug', $slug)->first();
        if (!$page) {
            throw new BizException('页面不存在');
        }
        return $page->toArray();
    }

    /**
     * 创建页面
     *
     * @param array $data
     * @return array
     */
    public function createPage(array $data)
    {

        // 生成slug
        if (empty($data['slug']) || !isset($data['slug'])) {
            $data['slug'] = EditorPage::generateSlug($data['title']);
        }

        // 设置创建者ID
        $data['creator_id'] = auth()->id() ?? 0;

        // 创建页面
        $page = new EditorPage();
        $page->fill($page->filterData($data));
        $page->save();

        return [
            'id' => $page->id,
            'slug' => $page->slug,
        ];
    }

    /**
     * 更新页面
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function updatePage(int $id, array $data): array
    {
        $page = EditorPage::find($id);
        if (!$page) {
            throw new BizException('页面不存在');
        }
        // 更新slug
        if (isset($data['title']) && (!isset($data['slug']) || empty($data['slug']))) {
            $data['slug'] = EditorPage::generateSlug($data['title']);
        }

        // 更新页面
        $page->fill($page->filterData($data));
        $page->save();

        return $this->getPageById($page->id);
    }

    /**
     * 删除页面
     *
     * @param int $id
     * @return bool
     */
    public function deletePage(int $id): bool
    {
        $page = EditorPage::find($id);
        if (!$page) {
            throw new BizException('页面不存在');
        }

        return $page->delete();
    }

    /**
     * 发布页面
     *
     * @param int $id
     * @return array
     */
    public function publishPage(int $id): array
    {
        $page = EditorPage::find($id);
        if (!$page) {
            throw new BizException('页面不存在');
        }

        $page->status = EditorPage::STATUS_PUBLISHED;
        $page->save();

        return $this->getPageById($page->id);
    }

    /**
     * 将页面设为草稿
     *
     * @param int $id
     * @return array
     */
    public function draftPage(int $id): array
    {
        $page = EditorPage::find($id);
        if (!$page) {
            throw new BizException('页面不存在');
        }

        $page->status = EditorPage::STATUS_DRAFT;
        $page->save();

        return $this->getPageById($page->id);
    }
}

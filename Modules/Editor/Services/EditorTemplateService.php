<?php

namespace Modules\Editor\Services;

use Bingo\Exceptions\BizException;
use Modules\Editor\Models\EditorTemplate;
use Modules\Editor\Models\EditorCategory;

class EditorTemplateService
{
    /**
     * 获取所有模板
     *
     * @return array
     */
    public function getAllTemplates(): array
    {
        return EditorTemplate::get()->toArray();
    }

    /**
     * 获取模板详情
     *
     * @param int $id
     * @return array
     */
    public function getTemplateById(int $id): array
    {
        $template = EditorTemplate::find($id);
        if (!$template) {
            throw new BizException('模板不存在');
        }
        return $template->toArray();
    }

    /**
     * 创建模板
     *
     * @param array $data
     * @return array
     */
    public function createTemplate(array $data): array
    {
        // 设置创建者ID
        $data['creator_id'] = auth()->id() ?? 0;
        
        // 创建模板
        $template = new EditorTemplate();
        $template->fill($template->filterData($data));
        $template->save();
        
        return $this->getTemplateById($template->id);
    }

    /**
     * 更新模板
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function updateTemplate(int $id, array $data): array
    {
        $template = EditorTemplate::find($id);
        if (!$template) {
            throw new BizException('模板不存在');
        }
        
        // 更新模板
        $template->fill($template->filterData($data));
        $template->save();
        
        return $this->getTemplateById($template->id);
    }

    /**
     * 删除模板
     *
     * @param int $id
     * @return bool
     */
    public function deleteTemplate(int $id): bool
    {
        $template = EditorTemplate::find($id);
        if (!$template) {
            throw new BizException('模板不存在');
        }
        
        // 系统模板不允许删除
        if ($template->is_system) {
            throw new BizException('系统模板不允许删除');
        }
        
        return $template->delete();
    }
}

<?php

namespace Modules\Donate\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Hash;

class DonateUser extends Model
{
    use SoftDeletes;

    protected $table = 'donate_users';
    
    protected $fillable = [
        'name',                 // 账户名
        'email',                // 电子邮件
        'phone',                // 手机号码
        'type',                 // 账户类型 (individual/business)
        'status',               // 状态 (active/inactive)
        'password',             // 密码
        'avatar',               // 头像路径
        'total_amount',         // 总捐款金额
        'remark',               // 备注
        'language',             // 语言偏好
        'last_login_at',        // 最后登录时间
        'login_failed_count',   // 登录失败次数
        'password_modified_at', // 密码最后修改时间
        'is_blacklisted',       // 是否黑名单
        'two_factor_enabled',   // 是否启用两因素认证
        'country',              // 国家/地区
        'city',                 // 城市
        'address'               // 地址
    ];
    
    protected $hidden = [
        'password'
    ];
    
    protected $casts = [
        'status' => 'string',
        'is_blacklisted' => 'boolean',
        'two_factor_enabled' => 'boolean',
        'last_login_at' => 'datetime',
        'password_modified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'login_failed_count' => 'integer',
        'total_amount' => 'decimal:2'
    ];

    /**
     * 设置密码
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Hash::make($value);
    }
    
    /**
     * 用户捐款记录
     */
    public function donations(): HasMany
    {
        return $this->hasMany(Donations::class, 'user_id');
    }
    
    /**
     * 获取用户类型标签
     */
    public function getTypeLabel(): string
    {
        $types = [
            'individual' => '个人账户',
            'business' => '企业账户'
        ];
        
        return $types[$this->type] ?? $this->type;
    }
    
    /**
     * 获取用户状态标签
     */
    public function getStatusLabel(): string
    {
        $statuses = [
            'active' => '启用',
            'inactive' => '禁用',
            'blacklisted' => '黑名单'
        ];
        
        return $statuses[$this->status] ?? $this->status;
    }
    
    /**
     * 是否为活跃用户
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
    
    /**
     * 是否启用两因素认证
     */
    public function hasTwoFactorEnabled(): bool
    {
        return $this->two_factor_enabled;
    }
    
    /**
     * 增加登录失败次数
     */
    public function incrementLoginFailedCount(): void
    {
        $this->login_failed_count++;
        $this->save();
    }
    
    /**
     * 重置登录失败次数
     */
    public function resetLoginFailedCount(): void
    {
        $this->login_failed_count = 0;
        $this->save();
    }
    
    /**
     * 更新最后登录时间
     */
    public function updateLastLogin(): void
    {
        $this->last_login_at = now();
        $this->save();
    }
    
    /**
     * 更新密码修改时间
     */
    public function updatePasswordModifiedAt(): void
    {
        $this->password_modified_at = now();
        $this->save();
    }
}
<template>
  <div class="user-list-container">
    <!-- 搜索过滤区域 -->
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <div class="actions">
            <el-button type="primary" @click="refreshData" :loading="loading" size="small">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="searchForm" label-position="top" @submit.prevent>
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="关键词搜索">
              <el-input 
                v-model="searchForm.keyword" 
                placeholder="搜索用户名、邮箱或电话" 
                clearable
                @keyup.enter="handleSearch"
                class="filter-input"
              />
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="用户状态">
              <el-select 
                v-model="searchForm.status" 
                placeholder="全部" 
                clearable 
                class="filter-input"
              >
                <el-option label="活跃" value="active" />
                <el-option label="非活跃" value="inactive" />
                <el-option label="黑名单" value="blacklist" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="用户类型">
              <el-select 
                v-model="searchForm.type" 
                placeholder="全部" 
                clearable 
                class="filter-input"
              >
                <el-option v-for="item in userTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="注册日期">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                :shortcuts="dateShortcuts"
                class="date-range-picker"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item class="action-buttons">
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon> 搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon> 重置
              </el-button>
              <el-button type="success" @click="addUser">
                <el-icon><Plus /></el-icon> 添加用户
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 用户表格区域 -->
    <el-card class="user-table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="userStore.users"
        border
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="用户名" min-width="120" show-overflow-tooltip />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="phone" label="电话" min-width="120" show-overflow-tooltip />
        <el-table-column label="用户类型" width="100">
          <template #default="scope">
            <el-tag :type="getUserTypeTag(scope.row.type)">
              {{ getUserType(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)" 
              :effect="scope.row.status === 'active' ? 'light' : 'plain'"
            >
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="捐款金额" min-width="120">
          <template #default="scope">
            $ {{ formatAmount(scope.row.total_amount || scope.row.donation_amount || 0) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" min-width="170" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="editUser(scope.row)">编辑</el-button>
            <el-button type="info" link @click="viewDetail(scope.row)">详情</el-button>
            <el-button type="success" link @click="enableUser(scope.row)">启用账户</el-button>
            <el-button type="warning" link @click="resetPassword(scope.row)">重置密码</el-button>
            <el-popconfirm 
              title="确定要删除该用户吗？" 
              @confirm="deleteUser(scope.row)"
            >
              <template #reference>
                <el-button type="danger" link>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="userStore.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, 
  Plus, 
  Refresh,
  MoreFilled, 
  ArrowDown
} from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/userStore'
import type { User, UserFilter } from '../../types/user'

const router = useRouter()
const userStore = useUserStore()

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive<UserFilter>({
  keyword: '',
  status: undefined,
  type: undefined,
  date_range: null
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 用户类型选项
const userTypes = ref([
  { label: '个人', value: 'individual' },
  { label: '企业', value: 'business' }
])

// 计算属性
const loading = ref(false)
const selectedRows = ref<User[]>([])

// 监听日期变化
watch(dateRange, (newValue) => {
  searchForm.date_range = newValue
})

// 初始化
onMounted(() => {
  loadData()
})

// 加载数据方法
const loadData = () => {
  loading.value = true
  
  userStore.fetchUsers(searchForm, currentPage.value, pageSize.value)
    .then(() => {
      console.log('获取用户列表成功，数据:', userStore.users)
      loading.value = false
    })
    .catch(error => {
      console.error('获取用户列表失败:', error)
      ElMessage.error('获取用户列表失败，请稍后重试')
      loading.value = false
    })
}

// 刷新数据
const refreshData = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    // @ts-ignore - 类型错误可以使用ts-ignore跳过
    searchForm[key] = undefined
  })
  dateRange.value = null
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 用户选择变化
const handleSelectionChange = (selection: User[]) => {
  selectedRows.value = selection
}

// 添加用户
const addUser = () => {
  router.push('/donate/users/create')
}

// 编辑用户
const editUser = (user: User) => {
  router.push(`/donate/users/${user.id}/edit`)
}

// 查看详情
const viewDetail = (user: User) => {
  router.push(`/donate/users/${user.id}`)
}

// 删除用户
const deleteUser = async (user: User) => {
  try {
    await userStore.deleteUser(user.id)
    ElMessage.success(`已成功删除用户: ${user.name}`)
    loadData()
  } catch (error: any) {
    ElMessage.error(`删除用户失败: ${error.message}`)
  }
}

// 启用用户账户
const enableUser = (user: User) => {
  userStore.updateUserStatus(user.id, 'active')
    .then(() => {
      ElMessage.success(`已成功启用用户账户: ${user.name}`)
      loadData() // 刷新列表数据
    })
    .catch(error => {
      ElMessage.error(`启用用户账户失败: ${error.message || '未知错误'}`)
    })
}

// 重置用户密码
const resetPassword = (user: User) => {
  ElMessageBox.confirm(
    `确定要重置用户 ${user.name} 的密码吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      userStore.resetUserPassword(user.id)
        .then(() => {
          ElMessage.success(`密码已重置并发送至用户 ${user.name} 的邮箱`)
        })
        .catch(error => {
          ElMessage.error(`重置密码失败: ${error.message || '未知错误'}`)
        })
    })
    .catch(() => {
      ElMessage.info('已取消重置密码操作')
    })
}

// 获取用户类型标签
const getUserType = (type: string): string => {
  const types: Record<string, string> = {
    'individual': '个人',
    'business': '企业'
  }
  return types[type] || type || '--'
}

// 获取用户类型标签样式
const getUserTypeTag = (type: string): string => {
  const tags: Record<string, string> = {
    'individual': '',
    'business': 'success'
  }
  return tags[type] || ''
}

// 获取状态标签
const getStatusLabel = (status: string): string => {
  const statuses: Record<string, string> = {
    'active': '活跃',
    'inactive': '非活跃',
    'blacklist': '黑名单'
  }
  return statuses[status] || status || '--'
}

// 获取状态标签类型
const getStatusType = (status: string): string => {
  switch (status) {
    case 'active': return 'success'
    case 'inactive': return 'info'
    case 'blacklist': return 'danger'
    default: return 'info'
  }
}

// 格式化日期
const formatDate = (date: string): string => {
  return date || '--'
}

// 格式化金额
const formatAmount = (amount: number): string => {
  if (amount === undefined || amount === null) return '0.00'
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.user-table-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 设置表单项标签样式 */
:deep(.el-form-item__label) {
  padding-bottom: 4px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

/* 统一所有输入类组件样式 */
.filter-input {
  width: 100%;
  height: 36px;
}

/* 统一日期选择器样式 */
.date-range-picker {
  width: 100%;
  height: 36px;
}

/* 确保所有输入框内部样式一致 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-date-editor .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  transition: box-shadow 0.2s;
  background-color: #fff;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select .el-input__wrapper:hover),
:deep(.el-date-editor .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus),
:deep(.el-date-editor .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 按钮样式优化 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.action-buttons .el-button {
  margin-left: 10px;
  height: 36px;
  padding: 8px 16px;
  border-radius: 4px;
}

.action-buttons .el-button .el-icon {
  margin-right: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .action-buttons {
    justify-content: flex-start;
  }
  
  .action-buttons .el-button:first-child {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .date-range-picker {
    width: 100%;
  }
}
</style> 
<template>
  <div class="donation-records-tab">
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="stat-card">
            <div class="stat-title">总捐款</div>
            <div class="stat-value">${{ formatAmount(totalAmount) }}</div>
            <div class="stat-footer">
              <span class="trend up">
                <el-icon><ArrowUp /></el-icon> 
                12.5%
              </span>
              vs去年
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="stat-card">
            <div class="stat-title">平均捐款</div>
            <div class="stat-value">${{ formatAmount(averageAmount) }}</div>
            <div class="stat-footer">
              <span class="trend down">
                <el-icon><ArrowDown /></el-icon> 
                3.2%
              </span>
              vs去年
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="stat-card">
            <div class="stat-title">捐款次数</div>
            <div class="stat-value">{{ donationCount }}</div>
            <div class="stat-footer">
              <span class="trend up">
                <el-icon><ArrowUp /></el-icon> 
                8.7%
              </span>
              vs去年
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="stat-card">
            <div class="stat-title">上次捐款</div>
            <div class="stat-value-date">{{ lastDonationDate }}</div>
            <div class="stat-footer">
              {{ lastDonationAmount ? '$' + formatAmount(lastDonationAmount) : '--' }}
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <div class="filter-row">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="-"
        :start-placeholder="$t('Donate.userDetail.donationRecords.startDate')"
        :end-placeholder="$t('Donate.userDetail.donationRecords.endDate')"
        :shortcuts="dateShortcuts"
      />
      
      <el-select 
        v-model="statusFilter" 
        :placeholder="$t('Donate.userDetail.donationRecords.status')"
        clearable
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      
      <el-select 
        v-model="paymentMethodFilter" 
        :placeholder="$t('Donate.userDetail.donationRecords.paymentMethod')"
        clearable
      >
        <el-option
          v-for="item in paymentMethodOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      
      <el-button type="primary" @click="handleFilter">
        {{ $t('Donate.userDetail.donationRecords.filter') }}
      </el-button>
      
      <el-button @click="resetFilter">
        {{ $t('Donate.userDetail.donationRecords.reset') }}
      </el-button>
    </div>
    
    <el-table
      :data="donationRecords"
      v-loading="loading"
      border
      style="width: 100%"
    >
      <el-table-column 
        prop="id" 
        :label="$t('Donate.userDetail.donationRecords.id')" 
        width="80" 
      />
      
      <el-table-column 
        prop="donation_time" 
        :label="$t('Donate.userDetail.donationRecords.donationTime')" 
        width="180" 
      />
      
      <el-table-column 
        prop="amount" 
        :label="$t('Donate.userDetail.donationRecords.amount')" 
        width="120" 
      >
        <template #default="{ row }">
          ${{ formatAmount(row.amount) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="payment_method" 
        :label="$t('Donate.userDetail.donationRecords.paymentMethod')" 
        width="150" 
      >
        <template #default="{ row }">
          <div class="payment-method">
            <img :src="getPaymentIcon(row.payment_method)" :alt="row.payment_method" class="payment-icon" />
            {{ getPaymentMethodText(row.payment_method) }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="transaction_id" 
        :label="$t('Donate.userDetail.donationRecords.transactionId')" 
      />
      
      <el-table-column 
        prop="status" 
        :label="$t('Donate.userDetail.donationRecords.status')" 
        width="120"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getStatusType(row.status)"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        :label="$t('Donate.userDetail.donationRecords.actions')" 
        width="150"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button type="primary" link size="small" @click="viewDetail(row)">
            {{ $t('Donate.userDetail.donationRecords.viewDetail') }}
          </el-button>
          <el-button type="success" link size="small" @click="sendReceipt(row)" :disabled="row.status !== 'completed'">
            {{ $t('Donate.userDetail.donationRecords.sendReceipt') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 捐款详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="$t('Donate.userDetail.donationRecords.donationDetail')"
      width="600px"
    >
      <div v-if="selectedDonation" class="donation-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.id')">
            {{ selectedDonation.id }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.donationTime')">
            {{ selectedDonation.donation_time }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.amount')">
            ${{ formatAmount(selectedDonation.amount) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.paymentMethod')">
            {{ getPaymentMethodText(selectedDonation.payment_method) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.transactionId')">
            {{ selectedDonation.transaction_id }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.status')">
            <el-tag :type="getStatusType(selectedDonation.status)">
              {{ getStatusText(selectedDonation.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('Donate.userDetail.donationRecords.note')">
            {{ selectedDonation.note || '--' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">
            {{ $t('Donate.userDetail.donationRecords.close') }}
          </el-button>
          <el-button 
            type="primary" 
            @click="sendReceipt(selectedDonation)"
            :disabled="!selectedDonation || selectedDonation.status !== 'completed'"
          >
            {{ $t('Donate.userDetail.donationRecords.sendReceipt') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, defineProps } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { useUserStore } from '../../../stores/userStore'
import type { DonationRecord } from '../../../types/user'

const props = defineProps<{
  userId: number;
}>()

const { t } = useI18n()

const userStore = useUserStore()

// 状态变量
const loading = ref(false)
const donationRecords = ref<DonationRecord[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dateRange = ref<[string, string] | null>(null)
const statusFilter = ref('')
const paymentMethodFilter = ref('')
const detailDialogVisible = ref(false)
const selectedDonation = ref<DonationRecord | null>(null)

// 统计数据
const totalAmount = ref(2500)
const averageAmount = ref(125)
const donationCount = ref(20)
const lastDonationDate = ref('2025-01-28')
const lastDonationAmount = ref(150)

// 状态选项
const statusOptions = [
  { value: 'completed', label: '已完成' },
  { value: 'pending', label: '处理中' },
  { value: 'failed', label: '失败' },
  { value: 'refunded', label: '已退款' }
]

// 支付方式选项
const paymentMethodOptions = [
  { value: 'credit_card', label: '信用卡' },
  { value: 'paypal', label: 'PayPal' },
  { value: 'stripe', label: 'Stripe' },
  { value: 'bank_transfer', label: '银行转账' }
]

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  }
]

// 加载数据
const loadRecords = async () => {
  loading.value = true
  
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      payment_method: paymentMethodFilter.value || undefined,
      status: statusFilter.value || undefined,
      start_date: dateRange.value ? dateRange.value[0] : undefined,
      end_date: dateRange.value ? dateRange.value[1] : undefined
    }
    
    const result = await userStore.getUserDonationRecords(props.userId, params)
    donationRecords.value = result.records
    total.value = result.total
  } catch (error: any) {
    ElMessage.error(`加载捐款记录失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleDateChange = () => {
  // 日期变更时不立即触发查询，等待用户点击筛选按钮
}

const handleMethodChange = () => {
  // 支付方式变更时不立即触发查询，等待用户点击筛选按钮
}

const handleStatusChange = () => {
  // 状态变更时不立即触发查询，等待用户点击筛选按钮
}

const handleFilter = () => {
  currentPage.value = 1
  loadRecords()
}

const resetFilter = () => {
  dateRange.value = null
  statusFilter.value = ''
  paymentMethodFilter.value = ''
  currentPage.value = 1
  loadRecords()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadRecords()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadRecords()
}

const sendReceipt = (record: DonationRecord) => {
  ElMessageBox.confirm(
    '确定要向用户发送捐款收据吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  )
    .then(() => {
      userStore.sendDonationReceipt(props.userId, record.id)
        .then(() => {
          ElMessage.success('捐款收据已发送')
        })
        .catch(error => {
          ElMessage.error(`发送捐款收据失败: ${error.message}`)
        })
    })
    .catch(() => {
      ElMessage.info('已取消发送操作')
    })
}

// 格式化方法
const formatDate = (date: string): string => {
  return date || '--'
}

const formatAmount = (amount: number): string => {
  return amount ? `¥${amount.toLocaleString()}` : '¥0.00'
}

const formatPaymentMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    'credit_card': '信用卡',
    'wechat': '微信支付',
    'alipay': '支付宝',
    'bank_transfer': '银行转账'
  }
  return methodMap[method] || method
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'completed': '已完成',
    'pending': '处理中',
    'failed': '失败',
    'refunded': '已退款'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'failed': 'danger',
    'refunded': 'info'
  }
  return typeMap[status] || ''
}

// 获取支付方式文本
const getPaymentMethodText = (method: string): string => {
  const methodMap: Record<string, string> = {
    'credit_card': '信用卡',
    'paypal': 'PayPal',
    'stripe': 'Stripe',
    'bank_transfer': '银行转账'
  }
  return methodMap[method] || method
}

// 获取支付方式图标
const getPaymentIcon = (method: string): string => {
  const iconMap: Record<string, string> = {
    'credit_card': '/images/payment/credit_card.svg',
    'paypal': '/images/payment/paypal.svg',
    'stripe': '/images/payment/stripe.svg',
    'bank_transfer': '/images/payment/bank.svg'
  }
  return iconMap[method] || '/images/payment/default.svg'
}

// 查看详情
const viewDetail = (donation: DonationRecord) => {
  selectedDonation.value = donation
  detailDialogVisible.value = true
}

onMounted(() => {
  loadRecords()
})
</script>

<style lang="scss" scoped>
.donation-records-tab {
  padding: 20px 0;
  
  .stats-cards {
    margin-bottom: 30px;
    
    .stat-card {
      height: 100%;
      
      .stat-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      .stat-value-date {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .stat-footer {
        font-size: 12px;
        color: #909399;
        
        .trend {
          display: inline-flex;
          align-items: center;
          
          &.up {
            color: #67C23A;
          }
          
          &.down {
            color: #F56C6C;
          }
        }
      }
    }
  }
  
  .filter-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }
  
  .payment-method {
    display: flex;
    align-items: center;
    gap: 5px;
    
    .payment-icon {
      width: 20px;
      height: 20px;
    }
  }
  
  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .donation-detail {
    max-height: 60vh;
    overflow: auto;
  }
}

@media (max-width: 768px) {
  .donation-records-tab {
    .stats-cards {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style> 
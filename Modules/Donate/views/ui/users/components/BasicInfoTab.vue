<template>
  <div class="basic-info-tab">
    <el-form 
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="user-form"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" maxlength="50" show-word-limit />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="用户类型" prop="type">
            <el-select v-model="form.type" class="w-100">
              <el-option
                v-for="item in userTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="电子邮箱" prop="email">
            <el-input v-model="form.email" type="email" />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="form.phone" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="国家/地区" prop="country">
            <el-select v-model="form.country" class="w-100">
              <el-option
                v-for="item in countries"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="城市" prop="city">
            <el-input v-model="form.city" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="address">
            <el-input v-model="form.address" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="语言" prop="language">
            <el-select v-model="form.language" class="w-100">
              <el-option
                v-for="item in languageOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input 
              v-model="form.remark" 
              type="textarea" 
              :rows="3" 
              maxlength="500" 
              show-word-limit 
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <div class="form-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, defineProps, defineEmits, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../../../stores/userStore'
import type { User } from '../../../types/user'

const props = defineProps<{
  user: User;
}>()

const emit = defineEmits<{
  (e: 'update', value: Partial<User>): void
}>()

const userStore = useUserStore()

const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  name: '',
  type: '',
  email: '',
  phone: '',
  country: '',
  city: '',
  address: '',
  remark: '',
  language: ''
})

// 用户类型选项
const userTypes = computed(() => userStore.userTypes)

// 国家选项
const countries = [
  { value: 'cn', label: '中国' },
  { value: 'hk', label: '中国香港' },
  { value: 'us', label: '美国' },
  { value: 'uk', label: '英国' },
  { value: 'jp', label: '日本' }
]

// 语言选项
const languageOptions = [
  { value: 'zh_CN', label: '简体中文' },
  { value: 'en', label: '英文' }
]

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入用户姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度应为2到50个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的电子邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^[+]?[\d\s-]{5,20}$/, message: '请输入有效的电话号码', trigger: 'blur' }
  ]
})

// 初始化表单数据
const initFormData = () => {
  if (props.user) {
    form.name = props.user.name || ''
    form.type = props.user.type || ''
    form.email = props.user.email || ''
    form.phone = props.user.phone || ''
    form.country = props.user.country || ''
    form.city = props.user.city || ''
    form.address = props.user.address || ''
    form.remark = props.user.remark || ''
    form.language = props.user.language || 'zh_CN'
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const updatedData = {
      name: form.name,
      type: form.type,
      email: form.email,
      phone: form.phone,
      country: form.country,
      city: form.city,
      address: form.address,
      remark: form.remark,
      language: form.language
    }
    
    emit('update', updatedData)
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
    initFormData()
  }
}

onMounted(() => {
  initFormData()
})
</script>

<style lang="scss" scoped>
.basic-info-tab {
  padding: 20px 0;
  
  .user-form {
    max-width: 100%;
  }
  
  .w-100 {
    width: 100%;
  }
}
</style> 
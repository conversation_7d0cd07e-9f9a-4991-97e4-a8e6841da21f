<template>
  <div class="login-records-tab">
    <div class="filter-section">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        @change="handleDateChange"
      />
      
      <el-select
        v-model="status"
        placeholder="登录状态"
        @change="handleStatusChange"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      
      <el-button type="primary" @click="handleFilter">
        筛选
      </el-button>
      <el-button @click="resetFilter">
        重置
      </el-button>
    </div>
    
    <el-table
      :data="records"
      v-loading="loading"
      style="width: 100%; margin-top: 20px;"
    >
      <el-table-column label="登录时间" prop="login_time" min-width="180" />
      <el-table-column label="IP地址" prop="ip_address" min-width="150" />
      <el-table-column label="设备" prop="device" min-width="180" />
      <el-table-column label="浏览器" prop="browser" min-width="150" />
      <el-table-column label="位置" prop="location" min-width="180" />
      <el-table-column label="状态" prop="status" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ formatStatus(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="失败原因" prop="fail_reason" min-width="200">
        <template #default="{ row }">
          <span v-if="row.status === 'failed'">{{ row.fail_reason || '未知原因' }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../../../stores/userStore'
import type { LoginRecord } from '../../../types/user'

const props = defineProps<{
  userId: number;
}>()

const userStore = useUserStore()

// 状态变量
const records = ref<LoginRecord[]>([])
const loading = ref(false)
const dateRange = ref<[string, string] | null>(null)
const status = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 状态选项
const statusOptions = [
  { value: 'success', label: '成功' },
  { value: 'failed', label: '失败' }
]

// 加载数据
const loadRecords = async () => {
  loading.value = true
  
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      status: status.value || undefined,
      start_date: dateRange.value ? dateRange.value[0] : undefined,
      end_date: dateRange.value ? dateRange.value[1] : undefined
    }
    
    const result = await userStore.getUserLoginRecords(props.userId, params)
    records.value = result.records
    total.value = result.total
  } catch (error: any) {
    ElMessage.error(`加载登录记录失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleDateChange = () => {
  // 日期变更时不立即触发查询，等待用户点击筛选按钮
}

const handleStatusChange = () => {
  // 状态变更时不立即触发查询，等待用户点击筛选按钮
}

const handleFilter = () => {
  currentPage.value = 1
  loadRecords()
}

const resetFilter = () => {
  dateRange.value = null
  status.value = ''
  handleFilter()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadRecords()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadRecords()
}

// 格式化方法
const formatDate = (date: string): string => {
  return date || '--'
}

const formatStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'success': '成功',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger'
  }
  return typeMap[status] || ''
}

onMounted(() => {
  loadRecords()
})
</script>

<style lang="scss" scoped>
.login-records-tab {
  padding: 20px 0;
  
  .filter-section {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 
<template>
  <div class="user-detail-container">
    <!-- 面包屑导航栏 -->
    <div class="breadcrumb-bar">
      <div class="breadcrumb-text">首页 > 用户管理 > {{ user.name || 'A' }}</div>
      <div class="close-btn" @click="goBack">
        <el-icon><Close /></el-icon>
      </div>
    </div>
    
    <!-- 主内容卡片 -->
    <div class="content-card">
      <div class="user-info-section">
        <!-- 左侧用户头像 -->
        <div class="user-avatar-container">
          <img 
            :src="user.avatar || '/images/default-avatar.png'" 
            alt="User avatar" 
            class="user-avatar" 
          />
          <div class="avatar-actions">
            <el-button type="primary" link class="update-avatar-btn">更新头像</el-button>
          </div>
        </div>
        
        <!-- 中间用户信息 -->
        <div class="user-details">
          <div class="info-row">
            <div class="info-label">状态</div>
            <div class="info-value">
              <el-tag type="success" class="status-tag" size="small">活跃</el-tag>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-label">登录失败次数</div>
            <div class="info-value">{{ user.login_failed_count || '10' }}</div>
          </div>
          
          <div class="info-row">
            <div class="info-label">创建时间</div>
            <div class="info-value">{{ user.created_at || '2024-09-23 19:16' }}</div>
          </div>
          
          <div class="info-row">
            <div class="info-label">账户设置</div>
            <div class="info-value">
              <el-switch v-model="accountSettingEnabled" active-color="#13ce66" />
              <span class="settings-text" v-if="accountSettingEnabled">查看设置</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧用户信息 -->
        <div class="user-extra-details">
          <div class="info-row">
            <div class="info-label">最后登录</div>
            <div class="info-value">{{ user.last_login_at || '2024-10-26 19:16' }}</div>
          </div>
          
          <div class="info-row">
            <div class="info-label">密码最后修改</div>
            <div class="info-value">{{ user.password_modified_at || '2024-10-23 19:16' }}</div>
          </div>
          
          <div class="info-row">
            <div class="info-label">两步验证</div>
            <div class="info-value">
              <el-switch v-model="twoFactorEnabled" active-color="#13ce66" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮区域 -->
      <!-- <div class="action-buttons-container">
        <el-button type="success" class="action-button" @click="enableUser">启用账户</el-button>
        <el-button type="primary" class="action-button" @click="resetPassword">重置密码</el-button>
        <el-button type="danger" class="action-button" @click="deleteUser">删除用户</el-button>
      </div> -->
      
      <!-- 选项卡区域 -->
      <div class="tabs-container">
        <el-tabs v-model="activeTab" class="user-tabs">
          <el-tab-pane label="基本信息" name="basicInfo">
            <div class="form-container">
              <div class="form-row">
                <div class="form-group">
                  <label class="required">姓名</label>
                  <el-input v-model="userForm.name" placeholder="请输入姓名">
                    <template #append>
                      <span class="char-count">0 / 50</span>
                    </template>
                  </el-input>
                </div>
                <div class="form-group">
                  <label class="required">用户类型</label>
                  <el-select v-model="userForm.type" placeholder="请选择">
                    <el-option label="个人" value="individual" />
                    <el-option label="企业" value="business" />
                  </el-select>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label class="required">电子邮箱</label>
                  <el-input v-model="userForm.email" placeholder="请输入电子邮箱" />
                </div>
                <div class="form-group">
                  <label>手机号码</label>
                  <el-input v-model="userForm.phone" placeholder="请输入手机号码" />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>国家/地区</label>
                  <el-select v-model="userForm.country" placeholder="Select">
                    <el-option label="中国" value="CN" />
                    <el-option label="香港" value="HK" />
                    <el-option label="美国" value="US" />
                  </el-select>
                </div>
                <div class="form-group">
                  <label>城市</label>
                  <el-input v-model="userForm.city" placeholder="请输入城市" />
                </div>
              </div>
              
              <div class="form-row full-width">
                <div class="form-group">
                  <label>详细地址</label>
                  <el-input v-model="userForm.address" type="textarea" rows="4" placeholder="请输入详细地址" />
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="登录记录" name="loginRecords">
            <component :is="loginRecordsComponent" :user-id="user.id" />
          </el-tab-pane>
          
          <el-tab-pane label="捐款记录" name="donationRecords">
            <component :is="donationRecordsComponent" :user-id="user.id" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { useUserStore } from '../../stores/userStore'
import type { User } from '../../types/user'

// 异步加载组件
const loginRecordsComponent = defineAsyncComponent(() => import('./components/LoginRecordsTab.vue'))
const donationRecordsComponent = defineAsyncComponent(() => import('./components/DonationRecordsTab.vue'))

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 用户数据
const user = computed(() => userStore.currentUser || {
  id: 0,
  name: '',
  email: '',
  phone: '',
  type: 'individual',
  status: 'active',
  donation_amount: 0,
  remark: '',
  avatar: null,
  last_login_at: '',
  login_failed_count: 0,
  password_modified_at: '',
  created_at: '',
  country: '',
  city: '',
  address: ''
} as User)

// 表单数据
const userForm = ref({
  name: '',
  type: '个人',
  email: '',
  phone: '',
  country: '',
  city: '',
  address: ''
})

// 状态变量
const activeTab = ref('basicInfo')
const twoFactorEnabled = ref(false)
const accountSettingEnabled = ref(true)
const loading = computed(() => userStore.loading)

// 初始化表单数据
const initFormData = () => {
  userForm.value = {
    name: user.value.name || '',
    type: user.value.type === 'business' ? '企业' : '个人',
    email: user.value.email || '',
    phone: user.value.phone || '',
    country: user.value.country || '',
    city: user.value.city || '',
    address: user.value.address || ''
  }
}

// 方法
const goBack = () => {
  router.push('/donate/users')
}

const loadUserData = async () => {
  const userId = Number(route.params.id)
  if (!userId) return
  
  try {
    const userData = await userStore.getUserById(userId)
    if (userData) {
      twoFactorEnabled.value = !!userData.two_factor_enabled
      initFormData()
    } else {
      ElMessage.error('用户不存在')
      router.push('/donate/users')
    }
  } catch (error: any) {
    ElMessage.error(`加载用户数据失败: ${error.message}`)
  }
}

const updatePhoto = () => {
  ElMessage.info('将更新用户头像')
}

const enableUser = () => {
  const userId = user.value.id
  
  userStore.updateUserStatus(userId, 'active')
    .then(() => {
      ElMessage.success('用户账户已启用')
    })
    .catch(error => {
      ElMessage.error(`启用用户账户失败: ${error.message}`)
    })
}

const resetPassword = () => {
  ElMessageBox.confirm(
    '确定要重置该用户的密码吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      userStore.resetUserPassword(user.value.id)
        .then(() => {
          ElMessage.success('密码已重置并发送至用户邮箱')
        })
        .catch(error => {
          ElMessage.error(`重置密码失败: ${error.message}`)
        })
    })
    .catch(() => {
      ElMessage.info('已取消重置密码操作')
    })
}

const deleteUser = () => {
  ElMessageBox.confirm(
    '确定要删除该用户吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      userStore.deleteUser(user.value.id)
        .then(() => {
          ElMessage.success('已成功删除用户')
          router.push('/donate/users')
        })
        .catch(error => {
          ElMessage.error(`删除用户失败: ${error.message}`)
        })
    })
    .catch(() => {
      ElMessage.info('已取消删除操作')
    })
}

const handleUserUpdate = (updatedData: Partial<User>) => {
  userStore.updateUser(user.value.id, updatedData)
    .then(() => {
      ElMessage.success('用户信息已更新')
    })
    .catch(error => {
      ElMessage.error(`更新用户信息失败: ${error.message}`)
    })
}

onMounted(() => {
  loadUserData()
})
</script>

<style scoped>
.user-detail-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* 面包屑导航栏样式 */
.breadcrumb-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

.breadcrumb-text {
  font-size: 14px;
  color: #606266;
}

.close-btn {
  cursor: pointer;
  font-size: 18px;
  color: #909399;
}

.close-btn:hover {
  color: #409EFF;
}

/* 内容卡片样式 */
.content-card {
  flex: 1;
  margin: 0px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  overflow: auto;
}

/* 用户信息区域样式 */
.user-info-section {
  display: flex;
  margin-bottom: 20px;
}

.user-avatar-container {
  width: 150px;
  margin-right: 30px;
}

.user-avatar {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.avatar-actions {
  margin-top: 10px;
  text-align: center;
}

.update-avatar-btn {
  padding: 0;
  height: auto;
}

.user-details, .user-extra-details {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.info-label {
  width: 100px;
  color: #606266;
  font-size: 14px;
}

.info-value {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #303133;
}

.status-tag {
  padding: 4px 10px;
}

.settings-text {
  color: #409EFF;
  cursor: pointer;
}

/* 操作按钮区域样式 */
.action-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 150px;
  margin-right: 30px;
}

.action-button {
  width: 100%;
}

/* 选项卡区域样式 */
.tabs-container {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.user-tabs {
  width: 100%;
}

/* 表单样式 */
.form-container {
  padding: 20px 0;
}

.form-row {
  display: flex;
  margin-bottom: 20px;
  gap: 20px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.form-group label.required::before {
  content: '* ';
  color: #f56c6c;
}

.full-width {
  width: 100%;
}

.char-count {
  font-size: 12px;
  color: #909399;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-info-section {
    flex-direction: column;
  }
  
  .user-avatar-container {
    margin: 0 auto 20px;
    text-align: center;
  }
  
  .form-row {
    flex-direction: column;
  }
}
</style> 
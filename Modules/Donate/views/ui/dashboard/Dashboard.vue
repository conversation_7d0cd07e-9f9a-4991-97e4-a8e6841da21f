<template>
  <div class="donate-dashboard">
    <!-- 统计卡片区域 -->
    <div class="stat-cards">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(card, index) in statCards" :key="index">
          <el-card class="stat-card" :class="card.class" shadow="hover">
            <div class="card-content">
              <div class="card-value">{{ card.value }}</div>
              <div class="card-label">{{ card.label }}</div>
            </div>
            <div class="card-icon">
              <el-icon><component :is="card.icon" /></el-icon>
            </div>
            <div class="card-link">
              <router-link :to="card.link">
                <el-icon><ArrowRight /></el-icon>
              </router-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 付款状态区域 -->
    <h3 class="section-title">付款状态</h3>
    <div class="payment-status">
      <el-row :gutter="20">
        <el-col :span="12" v-for="(card, index) in paymentCards" :key="index">
          <el-card class="payment-card" shadow="hover">
            <div class="payment-card-content">
              <div class="payment-card-icon">
                <div class="icon-placeholder">{{ card.iconText }}</div>
              </div>
              <div class="payment-card-info">
                <div class="payment-card-value">{{ card.value }}</div>
                <div class="payment-card-label">{{ card.label }}</div>
              </div>
            </div>
            <div class="payment-card-link">
              <router-link :to="card.link">
                <el-icon><ArrowRight /></el-icon>
              </router-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { 
  User, 
  Money, 
  Timer, 
  Star, 
  ArrowRight, 
  Wallet, 
  Document, 
  Warning, 
  TrendCharts 
} from '@element-plus/icons-vue'

// 统计卡片数据
const statCards = ref([
  {
    label: '总使用人数',
    value: 888,
    icon: User,
    link: '/donate/users',
    class: 'blue-card'
  },
  {
    label: '活跃用户',
    value: 666,
    icon: User,
    link: '/donate/users/active',
    class: 'green-card'
  },
  {
    label: '进行中campaign',
    value: 111,
    icon: Timer,
    link: '/donate/campaigns/ongoing',
    class: 'orange-card'
  },
  {
    label: '特回馈客户消息',
    value: 33,
    icon: Star,
    link: '/donate/users/vip',
    class: 'red-card'
  }
])

// 付款卡片数据
const paymentCards = ref([
  {
    label: '总付款金额',
    value: '$19999999',
    iconText: '圖標',
    link: '/donate/payments',
  },
  {
    label: '待付款数量',
    value: 111,
    iconText: '圖標',
    link: '/donate/payments/pending',
  },
  {
    label: '付款失败笔数',
    value: 22,
    iconText: '圖標',
    link: '/donate/payments/failed',
  },
  {
    label: '税务',
    value: '$ 123',
    iconText: '圖標',
    link: '/donate/tax',
  }
])
</script>

<style scoped>
.donate-dashboard {
  height: 80%;
  padding: 20px;
}

.dashboard-title {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
}

.section-title {
  margin: 30px 0 20px;
  font-size: 18px;
  font-weight: 500;
}

.stat-cards {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.blue-card {
  border-left: 4px solid #409EFF;
}

.green-card {
  border-left: 4px solid #67C23A;
}

.orange-card {
  border-left: 4px solid #E6A23C;
}

.red-card {
  border-left: 4px solid #F56C6C;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.card-label {
  font-size: 14px;
  color: #606266;
}

.card-icon {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 24px;
  opacity: 0.2;
}

.card-link {
  position: absolute;
  right: 20px;
  bottom: 20px;
}

.payment-card {
  height: 100px;
  margin-bottom: 20px;
  position: relative;
}

.payment-card-content {
  display: flex;
  align-items: center;
}

.payment-card-icon {
  margin-right: 20px;
}

.icon-placeholder {
  width: 60px;
  height: 60px;
  background-color: #95d475;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.payment-card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.payment-card-label {
  font-size: 14px;
  color: #606266;
}

.payment-card-link {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}
</style>

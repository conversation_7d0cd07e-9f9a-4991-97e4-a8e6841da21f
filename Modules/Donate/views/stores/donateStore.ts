import { defineStore } from 'pinia';
import { ref } from 'vue';
import http from '/admin/support/http';

interface DashboardData {
  totalUsers: number;
  activeUsers: number;
  ongoingCampaigns: number;
  vipUsers: number;
  totalAmount: string;
  pendingPayments: number;
  failedPayments: number;
  taxAmount: string;
}

export const useDonateStore = defineStore('donate', () => {
  const loading = ref(false);
  const dashboardData = ref<DashboardData>({
    totalUsers: 0,
    activeUsers: 0,
    ongoingCampaigns: 0,
    vipUsers: 0,
    totalAmount: '$0',
    pendingPayments: 0,
    failedPayments: 0,
    taxAmount: '$0'
  });

  // 获取仪表盘数据
  async function fetchDashboardData() {
    loading.value = true;
    try {
      const response = await http.get('/api/admin/donate/dashboard');
      if (response.data.code === 0) {
        dashboardData.value = response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch dashboard data');
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    dashboardData,
    fetchDashboardData
  };
});
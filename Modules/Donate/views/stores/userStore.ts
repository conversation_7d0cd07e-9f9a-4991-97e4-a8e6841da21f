import { defineStore } from 'pinia';
import { ref } from 'vue';
import http from '/admin/support/http';
import type { 
  User, 
  UserFilter, 
  LoginRecord, 
  DonationRecord, 
  UserListResponse,
  UserCreateData,
  UserUpdateData
} from '../types/user';
import { json } from 'stream/consumers';

export const useUserStore = defineStore('donateUser', () => {
  // 状态定义
  const users = ref<User[]>([]);
  const currentUser = ref<User | null>(null);
  const loading = ref(false);
  const total = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(10);
  
  // 用户类型选项
  const userTypes = ref([
    { value: 'individual', label: '个人' },
    { value: 'business', label: '企业' }
  ]);
  
  // 用户状态选项
  const userStatuses = ref([
    { value: 'active', label: '活跃' },
    { value: 'inactive', label: '非活跃' },
    { value: 'blacklist', label: '黑名单' }
  ]);
  
  // 获取用户列表
  async function fetchUsers(filter: UserFilter = {}, page: number = 1, perPage: number = 10) {
    loading.value = true;
    users.value = []; // 清空之前的数据
    
    try {
      const params = {
        page,
        per_page: perPage,
        ...filter
      };
      
      const response = await http.get('/donate/v2/users', { params });
      console.log("API响应:", response);
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        // 按照API返回格式获取数据
        users.value = response.data.data || [];
        total.value = response.data.total || 0;
        currentPage.value = response.data.page || page;
        pageSize.value = response.data.limit || perPage;
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        // 处理多层嵌套的情况
        const apiResponse = response.data.data;
        users.value = apiResponse.data || [];
        total.value = apiResponse.total || 0;
        currentPage.value = apiResponse.page || page;
        pageSize.value = apiResponse.limit || perPage;
      } else {
        console.error('API响应格式不符合预期:', response.data);
        users.value = [];
        total.value = 0;
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      users.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  }
  
  // 获取用户详情
  async function getUserById(id: number): Promise<User | null> {
    loading.value = true;
    currentUser.value = null;
    
    try {
      const response = await http.get(`/donate/v2/users/${id}`);
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        // 直接获取数据
        currentUser.value = response.data.data;
        return response.data.data;
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        // 处理多层嵌套的情况
        currentUser.value = response.data.data.data;
        return response.data.data.data;
      }
      return null;
    } catch (error) {
      console.error('获取用户详情失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  }
  
  // 创建用户
  async function createUser(userData: UserCreateData) {
    try {
      const response = await http.post('/donate/v2/users', userData);
      return response.data;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }
  
  // 更新用户
  async function updateUser(id: number, userData: UserUpdateData) {
    try {
      const response = await http.put(`/donate/v2/users/${id}`, userData);
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户数据
          currentUser.value = { ...currentUser.value, ...userData };
        }
        return response.data.data;
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户数据
          currentUser.value = { ...currentUser.value, ...userData };
        }
        return response.data.data.data;
      }
      return null;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  }
  
  // 删除用户
  async function deleteUser(id: number) {
    try {
      const response = await http.delete(`/donate/v2/users/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }
  
  // 更新用户状态
  async function updateUserStatus(id: number, status: string) {
    try {
      const response = await http.patch(`/donate/v2/users/${id}/status`, { status });
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户状态
          currentUser.value.status = status;
        }
        return response.data.data;
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户状态
          currentUser.value.status = status;
        }
        return response.data.data.data;
      }
      return null;
    } catch (error) {
      console.error('更新用户状态失败:', error);
      throw error;
    }
  }
  
  // 切换黑名单状态
  async function toggleBlacklist(id: number) {
    try {
      const response = await http.patch(`/donate/v2/users/${id}/blacklist`);
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户黑名单状态
          currentUser.value.is_blacklisted = !currentUser.value.is_blacklisted;
        }
        return response.data.data;
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户黑名单状态
          currentUser.value.is_blacklisted = !currentUser.value.is_blacklisted;
        }
        return response.data.data.data;
      }
      return null;
    } catch (error) {
      console.error('切换黑名单状态失败:', error);
      throw error;
    }
  }
  
  // 重置用户密码
  async function resetUserPassword(id: number) {
    try {
      const response = await http.post(`/donate/v2/users/${id}/reset-password`);
      return response.data;
    } catch (error) {
      console.error('重置用户密码失败:', error);
      throw error;
    }
  }
  
  // 切换两因素认证状态
  async function toggleTwoFactor(id: number) {
    try {
      const response = await http.patch(`/donate/v2/users/${id}/two-factor`);
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户两因素认证状态
          currentUser.value.two_factor_enabled = !currentUser.value.two_factor_enabled;
        }
        return response.data.data;
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        if (currentUser.value && currentUser.value.id === id) {
          // 更新当前用户两因素认证状态
          currentUser.value.two_factor_enabled = !currentUser.value.two_factor_enabled;
        }
        return response.data.data.data;
      }
      return null;
    } catch (error) {
      console.error('切换两因素认证状态失败:', error);
      throw error;
    }
  }
  
  // 获取用户登录记录
  async function getUserLoginRecords(userId: number, params: any = {}) {
    loading.value = true;
    
    try {
      const response = await http.get(`/donate/v2/users/${userId}/login-records`, { params });
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        return {
          records: response.data.data || [],
          total: response.data.total || 0
        };
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        return {
          records: response.data.data.data || [],
          total: response.data.data.total || 0
        };
      }
      return { records: [], total: 0 };
    } catch (error) {
      console.error('获取用户登录记录失败:', error);
      return { records: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }
  
  // 获取用户捐款记录
  async function getUserDonationRecords(userId: number, params: any = {}) {
    loading.value = true;
    
    try {
      const response = await http.get(`/donate/v2/users/${userId}/donation-records`, { params });
      
      if (response.data && (response.data.code === 0 || response.data.code === 200)) {
        return {
          records: response.data.data || [],
          total: response.data.total || 0
        };
      } else if (response.data && response.data.data && (response.data.data.code === 0 || response.data.data.code === 200)) {
        return {
          records: response.data.data.data || [],
          total: response.data.data.total || 0
        };
      }
      return { records: [], total: 0 };
    } catch (error) {
      console.error('获取用户捐款记录失败:', error);
      return { records: [], total: 0 };
    } finally {
      loading.value = false;
    }
  }
  
  // 发送捐款收据
  async function sendDonationReceipt(userId: number, donationId: number) {
    try {
      const response = await http.post(`/donate/v2/users/${userId}/send-receipt/${donationId}`);
      return response.data;
    } catch (error) {
      console.error('发送捐款收据失败:', error);
      throw error;
    }
  }
  
  return {
    users,
    currentUser,
    loading,
    total,
    currentPage,
    pageSize,
    userTypes,
    userStatuses,
    fetchUsers,
    getUserById,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus,
    toggleBlacklist,
    resetUserPassword,
    toggleTwoFactor,
    getUserLoginRecords,
    getUserDonationRecords,
    sendDonationReceipt
  };
}); 
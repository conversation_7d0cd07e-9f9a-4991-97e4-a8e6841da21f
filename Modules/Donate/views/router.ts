import { RouteRecordRaw } from 'vue-router';

const router: RouteRecordRaw[] = [
  {
    path: '/donate',
    component: () => import('/admin/layout/index.vue'),
    children: [
      {
        path: '',
        name: 'donate.dashboard',
        component: () => import('./ui/dashboard/Dashboard.vue'),
        meta: { title: '捐款仪表盘', icon: 'DataAnalysis' }
      },
      // {
      //   path: 'donations',
      //   name: 'donate.donations',
      //   component: () => import('./ui/donations/DonationList.vue'),
      //   meta: { title: '捐款列表', icon: 'List' }
      // },
      // {
      //   path: 'campaigns',
      //   name: 'donate.campaigns',
      //   component: () => import('./ui/campaigns/CampaignList.vue'),
      //   meta: { title: '捐款活动', icon: 'Calendar' }
      // },
      {
        path: 'users',
        name: 'donate.users',
        component: () => import('./ui/users/UserList.vue'),
        meta: { title: '捐款用户', icon: 'User' }
      },
      {
        path: 'users/:id',
        name: 'donate.userDetail',
        component: () => import('./ui/users/UserDetail.vue'),
        meta: { title: '用户详情', icon: 'User', hideInMenu: true }
      }
    ]
  }
];

export default router;
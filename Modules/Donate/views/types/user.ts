// Modules/Donate/views/types/user.ts
export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  type: string;
  status: string;
  donation_amount: number;
  remark?: string;
  avatar?: string | null;
  last_login_at?: string;
  login_failed_count?: number;
  password_modified_at?: string;
  created_at: string;
  updated_at?: string;
  country?: string;
  city?: string;
  address?: string;
  language?: string;
  two_factor_enabled?: boolean;
  is_blacklisted?: boolean;
}

export interface UserFilter {
  keyword?: string;
  status?: string;
  type?: string;
  date_range?: [string, string] | null;
}

export interface LoginRecord {
  id: number;
  login_at: string;
  ip_address: string;
  device: string;
  browser: string;
  os: string;
  status: string;
}

export interface DonationRecord {
  id: number;
  transaction_id: string;
  created_at: string;
  amount: number;
  payment_method: string;
  campaign_name: string;
  status: string;
}

export interface UserListResponse {
  data: User[];
  total: number;
  current_page: number;
  per_page: number;
}

export interface UserCreateData {
  name: string;
  email: string;
  phone?: string;
  type: string;
  country?: string;
  city?: string;
  address?: string;
  remark?: string;
  language?: string;
  password?: string;
}

export interface UserUpdateData {
  name?: string;
  email?: string;
  phone?: string;
  type?: string;
  country?: string;
  city?: string;
  address?: string;
  remark?: string;
  language?: string;
} 
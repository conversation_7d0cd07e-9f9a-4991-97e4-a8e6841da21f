<?php

use Illuminate\Support\Facades\Route;
use Modules\Donate\Admin\Controllers\DashboardController;
use Modules\Donate\Admin\Controllers\DonateActivityController;
use Modules\Donate\Admin\Controllers\DonateCategoryController;
use Modules\Donate\Admin\Controllers\DonateController;
use Modules\Donate\Admin\Controllers\DonateUserController;

Route::prefix('donate')->group(function () {

    Route::get('donate/create', [DonateController::class, 'create'])->name('bingo-admin.donate.create');
    Route::get('donate/{id}/edit', [DonateController::class, 'edit'])->name('bingo-admin.donate.edit');
    Route::apiResource('donate', DonateController::class)->names('bingo-admin.donate');

    Route::get('activity/create', [DonateActivityController::class, 'create'])->name('bingo-admin.donateActivity.create');
    Route::get('activity/{id}/edit', [DonateActivityController::class, 'edit'])->name('bingo-admin.donateActivity.edit');
    Route::apiResource('activity', DonateActivityController::class)->names('bingo-admin.donateActivity');

    Route::get('category/create', [DonateCategoryController::class, 'create'])->name('bingo-admin.donateCategory.create');
    Route::get('category/{id}/edit', [DonateCategoryController::class, 'edit'])->name('bingo-admin.donateCategory.edit');
    Route::apiResource('category', DonateCategoryController::class)->names('bingo-admin.donateCategory');
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index']);
    Route::get('/dashboard/stats', [DashboardController::class, 'getStats']);
    Route::get('/dashboard/recent-donations', [DashboardController::class, 'getRecentDonations']);
    Route::get('/dashboard/donation-trends', [DashboardController::class, 'getDonationTrends']);
    Route::get('/dashboard/top-donors', [DashboardController::class, 'getTopDonors']);
    
    // 用户管理 v1 API
    Route::prefix('users')->group(function () {
        Route::get('/', [DonateUserController::class, 'index']);
        Route::post('/', [DonateUserController::class, 'store']);
        Route::get('/{id}', [DonateUserController::class, 'show']);
        Route::put('/{id}', [DonateUserController::class, 'update']);
        Route::delete('/{id}', [DonateUserController::class, 'destroy']);
        Route::patch('/{id}/status', [DonateUserController::class, 'updateStatus']);
    });
    
    // 用户管理 v2 API
    Route::prefix('v2')->group(function () {
        Route::prefix('users')->group(function () {
            // 用户基本操作
            Route::get('/', [DonateUserController::class, 'index']);
            Route::post('/', [DonateUserController::class, 'store']);
            Route::get('/{id}', [DonateUserController::class, 'show']);
            Route::put('/{id}', [DonateUserController::class, 'update']);
            Route::delete('/{id}', [DonateUserController::class, 'destroy']);
            
            // 用户状态管理
            Route::patch('/{id}/status', [DonateUserController::class, 'updateStatus']);
            Route::patch('/{id}/blacklist', [DonateUserController::class, 'toggleBlacklist']);
            Route::patch('/{id}/two-factor', [DonateUserController::class, 'toggleTwoFactor']);
            
            // 用户密码管理
            Route::post('/{id}/reset-password', [DonateUserController::class, 'resetPassword']);
            
            // 用户记录查询
            Route::get('/{id}/login-records', [DonateUserController::class, 'getLoginRecords']);
            Route::get('/{id}/donation-records', [DonateUserController::class, 'getDonationRecords']);
            
            // 捐款收据
            Route::post('/{id}/send-receipt/{donationId}', [DonateUserController::class, 'sendReceipt']);
        });
    });
});

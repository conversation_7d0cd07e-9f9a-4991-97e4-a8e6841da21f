<?php

namespace Modules\Donate\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserStatusRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'status' => 'required|string|in:active,inactive'
        ];
    }
    
    public function messages()
    {
        return [
            'status.required' => '状态必填',
            'status.in' => '状态必须为活跃或非活跃'
        ];
    }
}
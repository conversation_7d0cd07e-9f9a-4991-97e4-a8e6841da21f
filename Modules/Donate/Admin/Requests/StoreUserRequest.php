<?php

namespace Modules\Donate\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'name' => 'required|string|max:50',
            'type' => 'required|string|in:individual,business',
            'email' => 'required|email|unique:donate_users,email|max:100',
            'phone' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:50',
            'city' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:200',
            'remark' => 'nullable|string|max:500',
            'language' => 'nullable|string|max:50',
            'password' => 'nullable|string|min:8'
        ];
    }
    
    public function messages()
    {
        return [
            'name.required' => '姓名必填',
            'name.max' => '姓名最大长度为50个字符',
            'type.required' => '用户类型必填',
            'type.in' => '用户类型必须为个人或企业',
            'email.required' => '邮箱必填',
            'email.email' => '邮箱格式不正确',
            'email.unique' => '该邮箱已被使用',
            'email.max' => '邮箱最大长度为100个字符',
            'phone.max' => '电话最大长度为20个字符',
            'country.max' => '国家/地区最大长度为50个字符',
            'city.max' => '城市最大长度为50个字符',
            'address.max' => '地址最大长度为200个字符',
            'remark.max' => '备注最大长度为500个字符',
            'language.max' => '语言最大长度为50个字符',
            'password.min' => '密码最少8个字符'
        ];
    }
}
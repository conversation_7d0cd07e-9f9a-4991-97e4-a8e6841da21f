<?php

namespace Modules\Donate\Admin\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\Donate\Models\DonateUser;
use Modules\Donate\Services\DashboardService;

class DashboardController extends Controller
{
    protected DashboardService $dashboardService;
    
    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }
    
    /**
     * 获取仪表盘首页数据
     */
    public function index(): JsonResponse
    {
        $data = [
            'stats' => $this->getStats()->getData(true),
            'recent_donations' => $this->getRecentDonations()->getData(true),
            'donation_trends' => $this->getDonationTrends()->getData(true),
            'top_donors' => $this->getTopDonors()->getData(true)
        ];
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data
        ]);
    }
    
    /**
     * 获取统计数据
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = $this->dashboardService->getStats();
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取最近捐款记录
     */
    public function getRecentDonations(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 5);
            $donations = $this->dashboardService->getRecentDonations($limit);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $donations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取捐款趋势数据
     */
    public function getDonationTrends(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', 'month'); // day, week, month, year
            $limit = $request->input('limit', 12);
            
            $trends = $this->dashboardService->getDonationTrends($period, $limit);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $trends
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取捐款排行榜
     */
    public function getTopDonors(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 10);
            $donors = $this->dashboardService->getTopDonors($limit);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $donors
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => $e->getMessage()
            ], 500);
        }
    }
} 
<?php

namespace Modules\Donate\Admin\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Donate\Admin\Requests\StoreUserRequest;
use Modules\Donate\Admin\Requests\UpdateUserRequest;
use Modules\Donate\Admin\Requests\UpdateUserStatusRequest;
use Modules\Donate\Services\UserService;
use Modules\Donate\Enums\UserErrorCode;
use Exception;

class DonateUserController extends Controller
{
    protected UserService $userService;
    
    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    
    /**
     * 获取用户列表
     */
    public function index(Request $request): mixed
    {
        $filter = $request->only(['keyword', 'status', 'type']);
        $perPage = $request->input('per_page', 10);
        
        return $this->userService->getUserList($filter, $perPage);
    }
    
    /**
     * 创建用户
     */
    public function store(StoreUserRequest $request): mixed
    {
        $userData = $request->validated();
        
        return $this->userService->createUser($userData);
    }
    
    /**
     * 获取用户详情
     */
    public function show(int $id): mixed
    {
        $user = $this->userService->getUserById($id);
        
        if (!$user) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return $user;
    }
    
    /**
     * 更新用户信息
     */
    public function update(UpdateUserRequest $request, int $id): mixed
    {
        $userData = $request->validated();
        
        $user = $this->userService->updateUser($id, $userData);
        
        if (!$user) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return $user;
    }
    
    /**
     * 删除用户
     */
    public function destroy(int $id): mixed
    {
        $result = $this->userService->deleteUser($id);
        
        if (!$result) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return true;
    }
    
    /**
     * 更新用户状态
     */
    public function updateStatus(UpdateUserStatusRequest $request, int $id): mixed
    {
        $status = $request->input('status');
        
        $user = $this->userService->updateUserStatus($id, $status);
        
        if (!$user) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return $user;
    }
    
    /**
     * 切换黑名单状态
     */
    public function toggleBlacklist(int $id): mixed
    {
        $user = $this->userService->toggleBlacklist($id);
        
        if (!$user) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return $user;
    }
    
    /**
     * 重置用户密码
     */
    public function resetPassword(int $id): mixed
    {
        $result = $this->userService->resetUserPassword($id);
        
        if (!$result) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return true;
    }
    
    /**
     * 切换两因素认证状态
     */
    public function toggleTwoFactor(int $id): mixed
    {
        $user = $this->userService->toggleTwoFactor($id);
        
        if (!$user) {
            throw new Exception(UserErrorCode::USER_NOT_FOUND->message(), UserErrorCode::USER_NOT_FOUND->value);
        }
        
        return $user;
    }
    
    /**
     * 获取用户捐款记录
     */
    public function getDonationRecords(Request $request, int $id): mixed
    {
        $filter = $request->only(['date_range', 'status', 'payment_method']);
        $perPage = $request->input('per_page', 10);
        
        return $this->userService->getUserDonationRecords($id, $filter, $perPage);
    }
    
    /**
     * 发送捐款收据
     */
    public function sendReceipt(int $id, int $donationId): mixed
    {
        $result = $this->userService->sendDonationReceipt($id, $donationId);
        
        if (!$result) {
            throw new Exception(UserErrorCode::DONATION_NOT_FOUND->message(), UserErrorCode::DONATION_NOT_FOUND->value);
        }
        
        return true;
    }
}
<?php

namespace Modules\Donate\Enums;

enum UserErrorCode: int
{
    /**
     * 用户相关错误码 (12000-12999)
     */
    case USER_NOT_FOUND = 12000;
    case USER_ALREADY_EXISTS = 12001;
    case USER_PASSWORD_INVALID = 12002;
    case USER_STATUS_INVALID = 12003;
    case USER_LOGIN_FAILED = 12004;
    case USER_LOGOUT_FAILED = 12005;
    case USER_TOKEN_INVALID = 12006;
    case DONATION_NOT_FOUND = 12007;
    
    /**
     * 获取错误信息
     */
    public function message(): string
    {
        return match ($this) {
            self::USER_NOT_FOUND => '用户不存在',
            self::USER_ALREADY_EXISTS => '用户已存在',
            self::USER_PASSWORD_INVALID => '密码无效',
            self::USER_STATUS_INVALID => '用户状态无效',
            self::USER_LOGIN_FAILED => '登录失败',
            self::USER_LOGOUT_FAILED => '登出失败',
            self::USER_TOKEN_INVALID => '用户令牌无效',
            self::DONATION_NOT_FOUND => '捐款记录不存在'
        };
    }
    
    /**
     * 获取HTTP状态码
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::USER_NOT_FOUND, self::DONATION_NOT_FOUND => 404,
            self::USER_ALREADY_EXISTS => 409,
            self::USER_PASSWORD_INVALID, self::USER_STATUS_INVALID, 
            self::USER_LOGIN_FAILED, self::USER_LOGOUT_FAILED,
            self::USER_TOKEN_INVALID => 400,
            default => 500,
        };
    }
}
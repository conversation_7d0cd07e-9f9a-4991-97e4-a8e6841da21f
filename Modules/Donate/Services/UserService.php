<?php

namespace Modules\Donate\Services;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Modules\Donate\Models\DonateUser;
use Modules\Donate\Models\Donations;

class UserService
{
    /**
     * 获取用户列表
     */
    public function getUserList(array $filter, int $perPage): LengthAwarePaginator
    {
        $query = DonateUser::query();
        
        // 关键词搜索
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('email', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if (!empty($filter['status'])) {
            $query->where('status', $filter['status']);
        }
        
        // 类型筛选
        if (!empty($filter['type'])) {
            $query->where('type', $filter['type']);
        }
        
        return $query->paginate($perPage);
    }
    
    /**
     * 获取单个用户详情
     */
    public function getUserById(int $id): ?DonateUser
    {
        $user = DonateUser::find($id);
        
        if ($user) {
            // 加载捐款总金额，如果没有存储在user表中，可以从捐款记录中计算
            if (!isset($user->total_amount)) {
                $donationAmount = Donations::where('user_id', $id)
                    ->where('status', 'completed')
                    ->sum('amount');
                    
                $user->total_amount = $donationAmount;
            }
        }
        
        return $user;
    }
    
    /**
     * 创建用户
     */
    public function createUser(array $data): DonateUser
    {
        if (isset($data['password'])) {
            // 密码会在模型的setPasswordAttribute方法中自动进行哈希处理
        } else {
            // 生成随机密码
            $password = Str::random(10);
            $data['password'] = $password;
            $data['raw_password'] = $password; // 用于邮件通知
        }
        
        // 设置默认状态和初始值
        $data['status'] = $data['status'] ?? 'active';
        $data['login_failed_count'] = 0;
        $data['total_amount'] = $data['total_amount'] ?? 0;
        $data['is_blacklisted'] = false;
        $data['two_factor_enabled'] = false;
        $data['password_modified_at'] = now();
        
        $user = DonateUser::create($data);
        
        // 发送通知邮件，可以在这里调用通知类
        // ...
        
        return $user;
    }
    
    /**
     * 更新用户信息
     */
    public function updateUser(int $id, array $data): ?DonateUser
    {
        $user = DonateUser::find($id);
        
        if (!$user) {
            return null;
        }
        
        $user->update($data);
        
        return $user;
    }
    
    /**
     * 删除用户
     */
    public function deleteUser(int $id): bool
    {
        $user = DonateUser::find($id);
        
        if (!$user) {
            return false;
        }
        
        return $user->delete();
    }
    
    /**
     * 更新用户状态
     */
    public function updateUserStatus(int $id, string $status): ?DonateUser
    {
        $user = DonateUser::find($id);
        
        if (!$user) {
            return null;
        }
        
        $user->status = $status;
        $user->save();
        
        return $user;
    }
    
    /**
     * 切换黑名单状态
     */
    public function toggleBlacklist(int $id): ?DonateUser
    {
        $user = DonateUser::find($id);
        
        if (!$user) {
            return null;
        }
        
        $user->is_blacklisted = !$user->is_blacklisted;
        if ($user->is_blacklisted) {
            $user->status = 'blacklisted';
        } else {
            $user->status = 'active';
        }
        $user->save();
        
        return $user;
    }
    
    /**
     * 重置用户密码
     */
    public function resetUserPassword(int $id): bool
    {
        $user = DonateUser::find($id);
        
        if (!$user) {
            return false;
        }
        
        // 生成随机密码
        $password = Str::random(10);
        $user->password = $password; // 会通过模型中的 setPasswordAttribute 方法自动哈希
        $user->updatePasswordModifiedAt();
        
        // 发送通知邮件，可以在这里调用通知类
        // ...
        
        return true;
    }
    
    /**
     * 切换两因素认证状态
     */
    public function toggleTwoFactor(int $id): ?DonateUser
    {
        $user = DonateUser::find($id);
        
        if (!$user) {
            return null;
        }
        
        $user->two_factor_enabled = !$user->two_factor_enabled;
        $user->save();
        
        return $user;
    }
    
    /**
     * 获取用户捐款记录
     */
    public function getUserDonationRecords(int $userId, array $filter, int $perPage): array
    {
        $query = Donations::where('user_id', $userId);
        
        // 日期范围
        if (!empty($filter['date_range'])) {
            $dates = explode(',', $filter['date_range']);
            if (count($dates) === 2) {
                $query->whereBetween('created_at', $dates);
            }
        }
        
        // 状态筛选
        if (!empty($filter['status'])) {
            $query->where('status', $filter['status']);
        }
        
        // 支付方式筛选
        if (!empty($filter['payment_method'])) {
            $query->where('payment_method', $filter['payment_method']);
        }
        
        $records = $query->orderBy('created_at', 'desc')->paginate($perPage);
        
        // 获取统计数据
        $stats = [
            'total_amount' => Donations::where('user_id', $userId)
                              ->where('status', 'completed')
                              ->sum('amount'),
            'average_amount' => Donations::where('user_id', $userId)
                              ->where('status', 'completed')
                              ->avg('amount'),
            'total_count' => Donations::where('user_id', $userId)
                              ->where('status', 'completed')
                              ->count(),
        ];
        
        // 获取最新一笔捐款
        $lastDonation = Donations::where('user_id', $userId)
                        ->where('status', 'completed')
                        ->orderBy('created_at', 'desc')
                        ->first();
        
        if ($lastDonation) {
            $stats['last_donation_date'] = $lastDonation->created_at->format('Y-m-d');
            $stats['last_donation_amount'] = $lastDonation->amount;
        }
        
        return [
            'records' => $records,
            'stats' => $stats
        ];
    }
    
    /**
     * 发送捐款收据
     */
    public function sendDonationReceipt(int $userId, int $donationId): bool
    {
        $donation = Donations::where('user_id', $userId)
                   ->where('id', $donationId)
                   ->where('status', 'completed')
                   ->first();
                   
        if (!$donation) {
            return false;
        }
        
        // 发送收据，可以在这里调用通知类
        // ...
        
        // 记录收据发送记录，如果有相关模型的话
        if (method_exists($donation, 'receipts')) {
            $donation->receipts()->create([
                'user_id' => $userId,
                'donation_id' => $donationId,
                'sent_at' => now()
            ]);
        }
        
        return true;
    }
}
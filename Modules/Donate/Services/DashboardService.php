<?php

namespace Modules\Donate\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Modules\Donate\Models\DonateUser;

class DashboardService
{
    /**
     * 获取统计数据
     *
     * @return array
     */
    public function getStats(): array
    {
        // 实际应用中应从数据库获取真实数据
        // 这里使用模拟数据
        return [
            'total_users' => DonateUser::count(),
            'total_donations' => 1250,
            'total_amount' => 1250000,
            'today_donations' => 25,
            'today_amount' => 15000,
            'active_campaigns' => 5
        ];
    }
    
    /**
     * 获取最近捐款记录
     *
     * @param int $limit
     * @return array
     */
    public function getRecentDonations(int $limit = 5): array
    {
        // 实际应用中应从数据库获取真实数据
        // 这里使用模拟数据
        $donations = [];
        
        for ($i = 0; $i < $limit; $i++) {
            $donations[] = [
                'id' => $i + 1,
                'user_id' => rand(1, 100),
                'user_name' => 'User ' . chr(65 + $i),
                'amount' => rand(100, 10000),
                'payment_method' => ['credit_card', 'wechat', 'alipay'][rand(0, 2)],
                'created_at' => Carbon::now()->subHours($i)->format('Y-m-d H:i:s')
            ];
        }
        
        return $donations;
    }
    
    /**
     * 获取捐款趋势数据
     *
     * @param string $period
     * @param int $limit
     * @return array
     */
    public function getDonationTrends(string $period = 'month', int $limit = 12): array
    {
        // 实际应用中应从数据库获取真实数据
        // 这里使用模拟数据
        $trends = [];
        
        switch ($period) {
            case 'day':
                for ($i = 0; $i < $limit; $i++) {
                    $date = Carbon::now()->subDays($limit - $i - 1);
                    $trends[] = [
                        'date' => $date->format('Y-m-d'),
                        'amount' => rand(5000, 20000),
                        'count' => rand(10, 50)
                    ];
                }
                break;
                
            case 'week':
                for ($i = 0; $i < $limit; $i++) {
                    $date = Carbon::now()->startOfWeek()->subWeeks($limit - $i - 1);
                    $trends[] = [
                        'date' => $date->format('Y-m-d'),
                        'amount' => rand(20000, 100000),
                        'count' => rand(50, 200)
                    ];
                }
                break;
                
            case 'year':
                for ($i = 0; $i < $limit; $i++) {
                    $date = Carbon::now()->startOfYear()->subYears($limit - $i - 1);
                    $trends[] = [
                        'date' => $date->format('Y'),
                        'amount' => rand(1000000, 5000000),
                        'count' => rand(1000, 5000)
                    ];
                }
                break;
                
            case 'month':
            default:
                for ($i = 0; $i < $limit; $i++) {
                    $date = Carbon::now()->startOfMonth()->subMonths($limit - $i - 1);
                    $trends[] = [
                        'date' => $date->format('Y-m'),
                        'amount' => rand(100000, 500000),
                        'count' => rand(200, 1000)
                    ];
                }
                break;
        }
        
        return $trends;
    }
    
    /**
     * 获取捐款排行榜
     *
     * @param int $limit
     * @return array
     */
    public function getTopDonors(int $limit = 10): array
    {
        // 实际应用中应从数据库获取真实数据
        // 这里使用模拟数据
        $donors = [];
        
        for ($i = 0; $i < $limit; $i++) {
            $donors[] = [
                'id' => $i + 1,
                'user_id' => rand(1, 100),
                'name' => 'User ' . chr(65 + $i),
                'avatar' => null,
                'total_amount' => rand(10000, 1000000),
                'donation_count' => rand(1, 50),
                'last_donation_at' => Carbon::now()->subDays(rand(1, 30))->format('Y-m-d H:i:s')
            ];
        }
        
        // 按捐款总额排序
        usort($donors, function ($a, $b) {
            return $b['total_amount'] - $a['total_amount'];
        });
        
        return $donors;
    }
} 
<template>
  <div class="bwms-module table-page">
    <div class="module-header">

      <!-- 添加导出按钮 -->
      <el-button class="button-no-border" @click="handleExport">
        <el-icon>
          <img src="/resources/admin/assets/icon/DownloadIcon.png" alt="DownloadIcon" />
        </el-icon>
        <span>{{ $t('Cms.list.export') }}</span>
      </el-button>
      
      <!-- 添加导入按钮 -->
      <el-button class="button-no-border" @click="handleImport">
        <el-icon>
          <img src="/resources/admin/assets/icon/UploadIcon.png" alt="UploadIcon" />
        </el-icon>
        <span>{{ $t('Cms.list.import') }}</span>
      </el-button>

      <!-- 筛选 -->
      <FilterPopover 
        v-model="showFilterDropdown"
      >
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
            <el-icon>
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('Cms.list.filter') }}</span>
          </el-button>
        </template>
        
        <el-form :model="search" label-position="top">
          <el-form-item :label="'ID'" class="filter-form-item">
            <el-input v-model="search.id" :placeholder="t('Cms.categories.input_placeholder')" size="default" />
          </el-form-item>
          <el-form-item :label="$t('Cms.list.title')" class="filter-form-item">
            <el-input v-model="search.title" :placeholder="t('Cms.categories.input_placeholder')+t('Cms.list.title')" size="default" />
          </el-form-item>
          <el-form-item :label="t('Cms.list.lang')" class="filter-form-item">
            <el-select v-model="search.data_lang"
            :placeholder="t('Cms.categories.select_placeholder')+t('Cms.list.lang')"
            size="default"
            style="width: 100%"
          >
              <el-option :label="langItem.label" :value="langItem.value" v-for="langItem in langList"
                :key="langItem.value" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item :label="t('Cms.list.category')" class="filter-form-item">
            <el-select
              v-model="search.category_id"
              :placeholder="t('Cms.categories.select_placeholder')+t('Cms.list.category')"
              size="default"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in categoryList"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
          </el-form-item>
        </el-form>
        
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="refreshContent">
              <el-icon><Refresh /></el-icon>
              <span>{{ $t('Cms.list.refresh') }}</span>
            </el-button>
            <el-button class="button-no-border" type="primary" @click="searchContent">
              <el-icon><Filter /></el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>

      <el-button class="button-no-border el-button-plus" @click="addContent" type="primary">
        <el-icon class="el-icon-custom">
          <img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" />
        </el-icon>
        <span style="color: var(--el-color-white);">{{ $t('Cms.list.add') }}</span>
      </el-button>
    </div>
    <div class="module-con">
      <div class="box">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
          <!-- 显示中 -->
          <el-tab-pane name="displaying">
            <template #label>
              <span class="tabs-tit">{{ t('Cms.list.tabs_name4') }}</span>
              <span class="tabs-num">{{ displayingTotal }}</span>
            </template>
            <el-table ref="displayingRefs" :data="displayingList" style="width: 100%; height: 100%"
              @selection-change="checkedContentHandle" v-loading="displayingLoading">
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template>
              <el-table-column type="selection" width="55" />
              <!-- <el-table-column prop="id" v-if="model_id !== 1" :label="'ID'" width="80">
                <template #default="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column> -->
              <el-table-column prop="title" :label="$t('Cms.list.title')" min-width="200">
                <template #default="scope">
                  <el-tooltip
                    :content="scope.row.title || '-'"
                    placement="top"
                    :max-width="300"
                    :show-after="200"
                  >
                    <div class="product-title-container" v-if="isProductModel">
                      <div class="product-image">
                        <el-image :src="scope.row.cover" :preview-src-list="[scope.row.cover]" fit="cover"
                          style="width: 60px; height: 60px; border-radius: 4px;" :initial-index="0" />
                      </div>
                      <div class="product-info">
                        <div class="product-title">{{ scope.row.title || '-' }}</div>
                        <div class="product-price" v-if="scope.row.price">{{ $t('Cms.list.price') }}: {{ scope.row.price
                          }}</div>
                        <div class="product-meta">
                          <span v-if="scope.row.color">{{ $t('Cms.list.color') }}: {{ scope.row.color }}</span>
                          <span v-if="scope.row.issuedate">{{ $t('Cms.list.issue_date') }}: {{ scope.row.issuedate
                            }}</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div class="text-ellipsis">{{ scope.row.title || '-' }}</div>
                      <div class="content-info">
                        <span>{{ $t('Cms.list.display') }}: {{ scope.row.display_text || $t('Cms.list.display') }}</span>
                        <span>{{ $t('Cms.list.delete') }}: {{ scope.row.delete_text || $t('Cms.list.delete') }}</span>
                        <span>{{ $t('Cms.list.new_page') }}: {{ scope.row.new_page_text || $t('Cms.list.new_page')
                          }}</span>
                        <span>{{ $t('Cms.list.url') }}: {{ scope.row.url || '/' }}</span>
                      </div>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="category_name" :label="$t('Cms.list.category')" width="180">
                <template #default="scope">
                  {{ scope.row.category_name || t('Cms.list.no_data') }}
                </template>
              </el-table-column>
              <el-table-column v-if="model_id == 1" prop="sort" :label="$t('Cms.list.sort')" width="180">
                <template #default="scope">
                  <span>{{ scope.row.sort || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('Cms.list.version_approve')" width="200">
                <template #default="scope">
                  <div class="approval-status">
                    <div>{{ scope.row.approval_text || '-' }}</div>
                    <div class="approval-date">• {{ scope.row.approval_date || '-' }}</div>
                    <div v-if="isProductModel && scope.row.verify_status" class="verify-status">
                      {{ $t('Cms.list.verify_status') }}: {{ getLocalizedVerifyStatus(scope.row.verify_status) }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.price')" width="120">
                <template #default="scope">
                  {{ scope.row.price || '-' }}
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.stock')" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.is_recommend === 1 ? 'success' : 'danger'" size="small" effect="light">
                    {{ scope.row.is_recommend === 1 ? $t('Cms.list.in_stock') : $t('Cms.list.out_of_stock') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" :label="$t('Cms.list.operate')" width="150">
                <template #default="scope">
                  <div class="bwms-operate-btn-box">
                    <el-button type="text" class="bwms-operate-btn" @click="upContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                      </el-icon>
                    </el-button>
                    <el-button type="text" class="bwms-operate-btn" @click="copyContentHandle(scope.row)"
                      :disabled="copyLoadingMap.get(scope.row.content_group_id)">
                      <el-icon v-if="!copyLoadingMap.get(scope.row.content_group_id)">
                        <img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" />
                      </el-icon>
                      <el-icon v-else class="is-loading">
                        <Loading />
                      </el-icon>
                    </el-button>
                    <el-button type="text" class="bwms-operate-btn" @click="delContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <!-- 所有 -->
          <el-tab-pane name="approved">
            <template #label>
              <span class="tabs-tit">{{ t('Cms.list.tabs_name1') }}</span>
              <span class="tabs-num">{{ approvedTotal }}</span>
            </template>
            <el-table ref="approvedRefs" :data="approvedList" style="width: 100%; height: 100%"
              @selection-change="checkedContentHandle" v-loading="approvedLoading">
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template>
              <el-table-column type="selection" width="55" />
              <!-- <el-table-column prop="id" v-if="model_id !== 1" :label="$t('Cms.list.id')" width="80">
                <template #default="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column> -->
              <el-table-column prop="title" :label="$t('Cms.list.title')" min-width="200">
                <template #default="scope">
                  <el-tooltip
                    :content="scope.row.title || '-'"
                    placement="top"
                    :max-width="300"
                    :show-after="200"
                  >
                    <div class="product-title-container" v-if="isProductModel">
                      <div class="product-image">
                        <el-image :src="scope.row.cover" :preview-src-list="[scope.row.cover]" fit="cover"
                          style="width: 60px; height: 60px; border-radius: 4px;" :initial-index="0" />
                      </div>
                      <div class="product-info">
                        <div class="product-title">{{ scope.row.title || '-' }}</div>
                        <div class="product-price" v-if="scope.row.price">{{ $t('Cms.list.price') }}: {{ scope.row.price
                          }}</div>
                        <div class="product-meta">
                          <span v-if="scope.row.color">{{ $t('Cms.list.color') }}: {{ scope.row.color }}</span>
                          <span v-if="scope.row.issuedate">{{ $t('Cms.list.issue_date') }}: {{ scope.row.issuedate
                            }}</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div class="text-ellipsis">{{ scope.row.title || '-' }}</div>
                      <div class="content-info">
                        <span>{{ $t('Cms.list.display') }}: {{ scope.row.display_text || $t('Cms.list.display') }}</span>
                        <span>{{ $t('Cms.list.delete') }}: {{ scope.row.delete_text || $t('Cms.list.delete') }}</span>
                        <span>{{ $t('Cms.list.new_page') }}: {{ scope.row.new_page_text || $t('Cms.list.new_page')
                          }}</span>
                        <span>{{ $t('Cms.list.url') }}: {{ scope.row.url || '/' }}</span>
                      </div>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="category_name" :label="$t('Cms.list.category')" width="180">
                <template #default="scope">
                  {{ scope.row.category_name || t('Cms.list.no_data') }}
                </template>
              </el-table-column>
              <el-table-column v-if="model_id == 1" prop="sort" :label="$t('Cms.list.sort')" width="180">
                <template #default="scope">
                  <span>{{ scope.row.sort || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('Cms.list.version_approve')" width="200">
                <template #default="scope">
                  <div class="approval-status">
                    <div v-if="scope.row.id % 5 === 3">由 Admin1 的拒絕</div>
                    <div v-else>{{ scope.row.approval_text || '-' }}</div>
                    <div class="approval-date" v-if="scope.row.id % 5 === 3">• {{ scope.row.approval_date || '-' }}
                    </div>
                    <div class="approval-date" v-else>• {{ scope.row.updated_at ? scope.row.updated_at.substring(0, 10)
                      : '03/07/2023' }}</div>
                    <div v-if="isProductModel && scope.row.verify_status" class="verify-status">
                      {{ $t('Cms.list.verify_status') }}: {{ getLocalizedVerifyStatus(scope.row.verify_status) }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.price')" width="120">
                <template #default="scope">
                  {{ scope.row.price || '-' }}
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.stock')" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.is_recommend === 1 ? 'success' : 'danger'" size="small" effect="light">
                    {{ scope.row.is_recommend === 1 ? $t('Cms.list.in_stock') : $t('Cms.list.out_of_stock') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" :label="$t('Cms.list.operate')" width="200">
                <template #default="scope">
                  <div class="bwms-operate-btn-box">
                    <el-button class="bwms-btn bwms-operate-btn" type="text" @click="upContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-btn bwms-operate-btn" type="text" @click="copyContentHandle(scope.row)"
                      :disabled="copyLoadingMap.get(scope.row.content_group_id)">
                      <el-icon v-if="!copyLoadingMap.get(scope.row.content_group_id)">
                        <img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" />
                      </el-icon>
                      <el-icon v-else class="is-loading">
                        <Loading />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-btn del-btn bwms-operate-btn" type="text"
                      @click="delContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <!-- 待审核 -->
          <el-tab-pane name="awaiting">
            <template #label>
              <span class="tabs-tit">{{ t('Cms.list.tabs_name2') }}</span>
              <span class="tabs-num">{{ awaitingTotal }}</span>
            </template>
            <el-table ref="awaitingRefs" :data="awaitingList" style="width: 100%; height: 100%"
              @selection-change="checkedContentHandle" v-loading="awaitingLoading">
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template>
              <el-table-column type="selection" width="55" />
              <!-- <el-table-column prop="id" v-if="model_id !== 1" :label="$t('Cms.list.id')" width="80">
                <template #default="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column> -->
              <el-table-column prop="title" :label="$t('Cms.list.title')" min-width="200">
                <template #default="scope">
                  <el-tooltip
                    :content="scope.row.title || '-'"
                    placement="top"
                    :max-width="300"
                    :show-after="200"
                  >
                    <div class="product-title-container" v-if="isProductModel">
                      <div class="product-image">
                        <el-image :src="scope.row.cover" :preview-src-list="[scope.row.cover]" fit="cover"
                          style="width: 60px; height: 60px; border-radius: 4px;" :initial-index="0" />
                      </div>
                      <div class="product-info">
                        <div class="product-title">{{ scope.row.title || '-' }}</div>
                        <div class="product-price" v-if="scope.row.price">{{ $t('Cms.list.price') }}: {{ scope.row.price
                          }}</div>
                        <div class="product-meta">
                          <span v-if="scope.row.color">{{ $t('Cms.list.color') }}: {{ scope.row.color }}</span>
                          <span v-if="scope.row.issuedate">{{ $t('Cms.list.issue_date') }}: {{ scope.row.issuedate
                            }}</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div class="text-ellipsis">{{ scope.row.title || '-' }}</div>
                      <div class="content-info">
                        <span>{{ $t('Cms.list.display') }}: {{ scope.row.display_text || $t('Cms.list.display') }}</span>
                        <span>{{ $t('Cms.list.delete') }}: {{ scope.row.delete_text || $t('Cms.list.delete') }}</span>
                        <span>{{ $t('Cms.list.new_page') }}: {{ scope.row.new_page_text || $t('Cms.list.new_page')
                          }}</span>
                        <span>{{ $t('Cms.list.url') }}: {{ scope.row.url || '/' }}</span>
                      </div>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="category_name" :label="$t('Cms.list.category')" width="180">
                <template #default="scope">
                  {{ scope.row.category_name || t('Cms.list.no_data') }}
                </template>
              </el-table-column>
              <el-table-column v-if="model_id == 1" prop="sort" :label="$t('Cms.list.sort')" width="180">
                <template #default="scope">
                  <span>{{ scope.row.sort || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('Cms.list.version_approve')" width="200">
                <template #default="scope">
                  <div class="approval-status">
                    <div>{{ scope.row.approval_text || '-' }}</div>
                    <div class="approval-date">• {{ scope.row.approval_date || '-' }}</div>
                    <div v-if="isProductModel && scope.row.verify_status" class="verify-status">
                      {{ $t('Cms.list.verify_status') }}: {{ getLocalizedVerifyStatus(scope.row.verify_status) }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.price')" width="120">
                <template #default="scope">
                  {{ scope.row.price || '-' }}
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.stock')" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.is_recommend === 1 ? 'success' : 'danger'" size="small" effect="light">
                    {{ scope.row.is_recommend === 1 ? $t('Cms.list.in_stock') : $t('Cms.list.out_of_stock') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" :label="$t('Cms.list.operate')" width="200">
                <template #default="scope">
                  <div class="bwms-operate-btn-box">
                    <el-button class="bwms-btn bwms-operate-btn" type="text" @click="upContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-btn bwms-operate-btn" type="text" @click="copyContentHandle(scope.row)"
                      :disabled="copyLoadingMap.get(scope.row.content_group_id)">
                      <el-icon v-if="!copyLoadingMap.get(scope.row.content_group_id)">
                        <img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" />
                      </el-icon>
                      <el-icon v-else class="is-loading">
                        <Loading />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-btn del-btn bwms-operate-btn" type="text" @click="delContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <!-- 草稿 -->
          <el-tab-pane name="draft">
            <template #label>
              <span class="tabs-tit">{{ t('Cms.list.tabs_name3') }}</span>
              <span class="tabs-num">{{ draftTotal }}</span>
            </template>
            <el-table ref="draftRefs" :data="draftList" style="width: 100%; height: 100%"
              @selection-change="checkedContentHandle" v-loading="draftLoading">
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template>
              <el-table-column type="selection" width="55" />
              <!-- <el-table-column prop="id" v-if="model_id !== 1" :label="$t('Cms.list.id')" width="80">
                <template #default="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column> -->
              <el-table-column prop="title" :label="$t('Cms.list.title')" min-width="200">
                <template #default="scope">
                  <el-tooltip
                    :content="scope.row.title || '-'"
                    placement="top"
                    :max-width="300"
                    :show-after="200"
                  >
                    <div class="product-title-container" v-if="isProductModel">
                      <div class="product-image">
                        <el-image :src="scope.row.cover" :preview-src-list="[scope.row.cover]" fit="cover"
                          style="width: 60px; height: 60px; border-radius: 4px;" :initial-index="0" />
                      </div>
                      <div class="product-info">
                        <div class="product-title">{{ scope.row.title || '-' }}</div>
                        <div class="product-price" v-if="scope.row.price">{{ $t('Cms.list.price') }}: {{ scope.row.price
                          }}</div>
                        <div class="product-meta">
                          <span v-if="scope.row.color">{{ $t('Cms.list.color') }}: {{ scope.row.color }}</span>
                          <span v-if="scope.row.issuedate">{{ $t('Cms.list.issue_date') }}: {{ scope.row.issuedate
                            }}</span>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div class="text-ellipsis">{{ scope.row.title || '-' }}</div>
                      <div class="content-info">
                        <span>{{ $t('Cms.list.display') }}: {{ scope.row.display_text || $t('Cms.list.display') }}</span>
                        <span>{{ $t('Cms.list.delete') }}: {{ scope.row.delete_text || $t('Cms.list.delete') }}</span>
                        <span>{{ $t('Cms.list.new_page') }}: {{ scope.row.new_page_text || $t('Cms.list.new_page')
                          }}</span>
                        <span>{{ $t('Cms.list.url') }}: {{ scope.row.url || '/' }}</span>
                      </div>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="category_name" :label="$t('Cms.list.category')" width="180">
                <template #default="scope">
                  {{ scope.row.category_name || t('Cms.list.no_data') }}
                </template>
              </el-table-column>
              <el-table-column v-if="model_id == 1" prop="sort" :label="$t('Cms.list.sort')" width="100">
                <template #default="scope">
                  <span>{{ scope.row.sort || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('Cms.list.version_approve')" width="200">
                <template #default="scope">
                  <div class="approval-status">
                    <div>{{ scope.row.approval_text || '-' }}</div>
                    <div class="approval-date">• {{ scope.row.approval_date || '-' }}</div>
                    <div v-if="isProductModel && scope.row.verify_status" class="verify-status">
                      {{ $t('Cms.list.verify_status') }}: {{ getLocalizedVerifyStatus(scope.row.verify_status) }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.price')" width="120">
                <template #default="scope">
                  {{ scope.row.price || '-' }}
                </template>
              </el-table-column>
              <el-table-column v-if="isProductModel" :label="$t('Cms.list.stock')" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.is_recommend === 1 ? 'success' : 'danger'" size="small" effect="light">
                    {{ scope.row.is_recommend === 1 ? $t('Cms.list.in_stock') : $t('Cms.list.out_of_stock') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="right" :label="$t('Cms.list.operate')" width="200">
                <template #default="scope">
                  <div class="bwms-operate-btn-box">
                    <el-button class="bwms-btn bwms-operate-btn" type="text" @click="upContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-btn bwms-operate-btn" type="text" @click="copyContentHandle(scope.row)"
                      :disabled="copyLoadingMap.get(scope.row.content_group_id)">
                      <el-icon v-if="!copyLoadingMap.get(scope.row.content_group_id)">
                        <img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" />
                      </el-icon>
                      <el-icon v-else class="is-loading">
                        <Loading />
                      </el-icon>
                    </el-button>
                    <el-button class="bwms-btn del-btn bwms-operate-btn" type="text" @click="delContentHandle(scope.row)">
                      <el-icon>
                        <img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="box-footer">
        <!-- 根据当前Tab显示对应的分页 -->
        <div class="pagination-container table-pagination-style" v-if="activeName === 'approved'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select v-model="approvedLimit" class="page-size-select" @change="approvedChangePage" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: approvedTotal }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination v-model:current-page="approvedPage" background layout="prev, pager, next"
              :page-size="approvedLimit" :total="approvedTotal" @current-change="approvedChangePage" />
          </div>
        </div>

        <!-- 正在展示Tab分页 -->
        <div class="pagination-container table-pagination-style" v-else-if="activeName === 'displaying'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select v-model="displayingLimit" class="page-size-select" @change="displayingChangePage" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: displayingTotal }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination v-model:current-page="displayingPage" background layout="prev, pager, next"
              :page-size="displayingLimit" :total="displayingTotal" @current-change="displayingChangePage" />
          </div>
        </div>

        <!-- 待审核Tab分页 -->
        <div class="pagination-container table-pagination-style" v-else-if="activeName === 'awaiting'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select v-model="awaitingLimit" class="page-size-select" @change="awaitingChangePage" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: awaitingTotal }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination v-model:current-page="awaitingPage" background layout="prev, pager, next"
              :page-size="awaitingLimit" :total="awaitingTotal" @current-change="awaitingChangePage" />
          </div>
        </div>

        <!-- 草稿Tab分页 -->
        <div class="pagination-container table-pagination-style" v-else-if="activeName === 'draft'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select v-model="draftLimit" class="page-size-select" @change="draftChangePage" size="default">
              <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size"
                class="page-size-option" />
              <template #empty>
                <div style="text-align: center; padding: 8px 0;">
                  {{ $t('Cms.list.no_data') }}
                </div>
              </template>
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: draftTotal }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination v-model:current-page="draftPage" background layout="prev, pager, next"
              :page-size="draftLimit" :total="draftTotal" @current-change="draftChangePage" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { useAppStore } from '/admin/stores/modules/app'

import { Category } from '../../domain/Category'
import CategoryRepositoryImpl from '../../infrastructure/CategoryRepositoryImpl'
import ContentRepositoryImpl from '../../infrastructure/ContentRepositoryImpl'
import CategoryServices from '../../application/CategoryServices'
import ContentServices from '../../application/ContentServices'
import http from '/admin/support/http'
import { useI18n } from 'vue-i18n'
import {
  Filter,
  Plus,
  Upload,
  Download,
  CopyDocument,
  Edit,
  Delete,
  Loading,
  ArrowDown,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FilterPopover from '/resources/admin/components/popover/index.vue'
const { t } = useI18n()

const api = 'cms'
const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
let lang = localStorage.getItem('bwms_language') || 'zh_HK'
const langList = ref([
  {
    label: t('Cms.list.select_opt1'),
    value: 'zh_CN',
  },
  {
    label: t('Cms.list.select_opt2'),
    value: 'en',
  },
  {
    label: t('Cms.list.select_opt3'),
    value: 'zh_HK',
  },
])
const model_id = ref(Number(route.query.model_id) || 0)
const pageName = ref(appStore.getPageName)

// Tab
const activeName = ref('approved')
const handleClick = (tabName: string) => {
  checkedContents.value = []
  approvedRefs.value?.clearSelection()
  displayingRefs.value?.clearSelection()
  awaitingRefs.value?.clearSelection()
  draftRefs.value?.clearSelection()

  // 根据不同的tab name获取对应数据，所有请求status都为0
  switch (tabName) {
    case 'approved':
      getApprovedList()
      break
    case 'displaying':
      getDisplayingList()
      break
    case 'awaiting':
      getAwaitingList()
      break
    case 'draft':
      getDraftList()
      break
  }
}

// 获取各标签的数量统计
const getTotalCounts = async () => {
  if (!model_id.value) return
  
  try {
    const res = await http.get(`/cms/content/total`, {
       model_id: model_id.value 
    })
    
    // 正确处理Axios响应对象，res.data包含API返回的完整数据
    if (res && res.data && res.data.code === 200 && res.data.data) {
      // 从正确的数据结构中获取各标签的数量
      approvedTotal.value = res.data.data.approved || 0
      displayingTotal.value = res.data.data.displaying || 0
      awaitingTotal.value = res.data.data.awaiting || 0
      draftTotal.value = res.data.data.draft || 0
    }
  } catch (error) {
    console.error('获取内容统计失败:', error)
  }
}

// 栏目
const CategoryInstance = new CategoryServices(new CategoryRepositoryImpl(api))
const categoryList = ref<Category[]>([])
const defaultProps = reactive({
  expandTrigger: 'hover' as const,
  emitPath: false,
  value: 'id',
  label: 'title',
  children: 'children',
})
async function getCategoryList(): Promise<void> {
  await CategoryInstance.get(model_id.value).then(res => {
    categoryList.value = res
  })
}

// 内容
const approvedRefs = ref<any>(null)
const displayingRefs = ref<any>(null)
const awaitingRefs = ref<any>(null)
const draftRefs = ref<any>(null)
const ContentInstance = new ContentServices(new ContentRepositoryImpl(api))
const approvedList = ref<Content[]>([])
const displayingList = ref<Content[]>([])
const awaitingList = ref<Content[]>([])
const draftList = ref<Content[]>([])
const approvedLoading = ref(true)
const displayingLoading = ref(true)
const awaitingLoading = ref(true)
const draftLoading = ref(true)
const checkedContents = ref<Content[]>([])
const checkedContentHandle = (val: Content[]) => {
  checkedContents.value = val
}
const upContentHandle = (content: Content) => {
  const { content_group_id } = content
  router.push({
    path: `/cms/cmsDetail`,
    query: {
      content_group_id,
      model_id: model_id.value,
      current_tab: activeName.value
    },
  })
}
const delContentHandle = (contents?: Content) => {
  ElMessageBox.confirm(
    t('Cms.list.dialog_con1'),
    t('Cms.list.dialog_tit1'),
    {
      confirmButtonText: t('Cms.list.confirm'),
      cancelButtonText: t('Cms.list.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      approvedLoading.value = true
      displayingLoading.value = true
      awaitingLoading.value = true
      draftLoading.value = true
      if (contents) checkedContents.value = [{ ...contents }]
      
      ContentInstance.delete(
        checkedContents.value.map(item => item.id),
        model_id.value,
      )
        .then(() => {
          searchContent()
          checkedContents.value.length = 0
          ElMessage.success(t('Cms.list.deleteSuccess'))
        })
        .finally(() => {
          approvedLoading.value = false
          displayingLoading.value = false
          awaitingLoading.value = false
          draftLoading.value = false
        })
    } catch (error) {
      ElMessage.error(t('Cms.list.deleteFailed'))
      approvedLoading.value = false
      displayingLoading.value = false
      awaitingLoading.value = false
      draftLoading.value = false
    }
  }).catch(() => {
  })
}
const addContent = () => {
  router.push({
    path: `/cms/cmsCreate`,
    query: {
      model_id: model_id.value,
      category_id: search.category_id ? search.category_id : categoryList.value[0].id || 0,
    },
  })
}
const copyLoadingMap = ref(new Map())
const copyContentHandle = async (content: Content) => {
  copyLoadingMap.value.set(content.content_group_id, true)

  try {
    await http.post('/cms/v2/content/0', {
      model_id: model_id.value,
      _copyId: content.content_group_id,
      locale: lang,
    })

    ElMessage.success(t('Cms.list.copy_success'))
    
    // 复制成功后更新统计数据
    await getTotalCounts()

    switch (activeName.value) {
      case 'approved':
        getApprovedList()
        break
      case 'displaying':
        getDisplayingList()
        break
      case 'awaiting':
        getAwaitingList()
        break
      case 'draft':
        getDraftList()
        break
    }
  } catch (error) {
    console.error('复制内容失败:', error)
    ElMessage.error(t('Cms.list.copy_failed'))
  } finally {
    copyLoadingMap.value.set(content.content_group_id, false)
  }
}

// 添加模拟的审批状态数据
const approvalStatusList = [
  { text: '由 Admin1 核准', date: '03/07/2023' },
  { text: '待審批', date: '03/07/2023' },
  { text: '由 Admin1 拒絕', date: '03/07/2023' },
  { text: '由 Admin1 拒絕', date: '正補充admin1於2023年1月11號佈預約V1.3版本' },
  { text: '由 Admin1 核准', date: '03/07/2023' }
];

// 修改审核状态文本函数，支持多语言
function getLocalizedVerifyStatus(status: number): string {
  const statusKey = `Cms.list.verify_status_${status}`;
  const fallbackKey = 'Cms.list.verify_status_unknown';

  // 检查翻译是否存在，如果不存在则使用回退翻译
  return t(statusKey) !== statusKey ? t(statusKey) : t(fallbackKey);
}

// 修改处理数据的方法，添加随机审批状态
function processContentData(items: any[]): Content[] {
  return items.map((item, index) => {
    // 获取一个随机审批状态
    const randomStatus = approvalStatusList[index % approvalStatusList.length];

    // 为产品类型添加特殊处理
    let displayText = '顯示中';
    let stockStatus = item.is_recommend === 1 ?
      t('Cms.list.in_stock') :
      t('Cms.list.out_of_stock');

    // 确保所有可能为null的字段有默认值
    return {
      ...item,
      id: item.id || 0,
      title: item.title || '',
      category_name: item.category_name || '',
      summary: item.summary || '',
      cover: item.cover || '',
      author: item.author || '',
      source: item.source || '',
      tags: item.tags || '',
      post_time: item.post_time || '',
      view_count: item.view_count || 0,
      comment_count: item.comment_count || 0,
      like_count: item.like_count || 0,
      alias: item.alias || '',
      seo_title: item.seo_title || '',
      seo_description: item.seo_description || '',
      seo_keywords: item.seo_keywords || '',
      content: item.content || '',
      is_recommend: item.is_recommend === null ? 0 : item.is_recommend,
      is_top: item.is_top === null ? 0 : item.is_top,
      status: item.status || 0,
      verify_status: item.verify_status || 0,
      approval_text: randomStatus.text,
      approval_date: randomStatus.date,
      display_text: displayText,
      delete_text: '禁止刪除',
      new_page_text: '禁止新增',
      url: '/',
      stock_status: stockStatus,
      price: item.price || '-',
      color: item.color || '-',
      issuedate: item.issuedate || '-',
      sort: item.sort || index + 1, // 如果没有sort值，使用索引+1作为默认值
    }
  })
}

// 修改isSpecialModel为isProductModel并添加更多判断逻辑
const isProductModel = computed(() => Number(model_id.value) === 3)

// 修改获取列表的方法，根据不同标签设置不同的status值
function getApprovedList() {
  if (!model_id.value) return

  approvedLoading.value = true
  const data = {
    model_id: model_id.value,
    limit: approvedLimit.value,
    page: approvedPage.value,
    status: 0, // approved状态下使用status=0
    ...search,
  }
  ContentInstance.get(data)
    .then(res => {
      approvedList.value = processContentData(res.items || [])
      // 不在这里更新total，而是使用getTotalCounts获取的值
    })
    .finally(() => {
      approvedLoading.value = false
    })
}

function getDisplayingList() {
  if (!model_id.value) return

  displayingLoading.value = true
  const data = {
    model_id: model_id.value,
    limit: displayingLimit.value,
    page: displayingPage.value,
    status: 1, // displaying状态下使用status=1
    tab: 'displaying', // 添加tab参数标识当前标签
    ...search,
  }
  ContentInstance.get(data)
    .then(res => {
      displayingList.value = processContentData(res.items || [])
      // 不在这里更新total，而是使用getTotalCounts获取的值
    })
    .finally(() => {
      displayingLoading.value = false
    })
}

function getAwaitingList() {
  if (!model_id.value) return

  awaitingLoading.value = true
  const data = {
    model_id: model_id.value,
    limit: awaitingLimit.value,
    page: awaitingPage.value,
    status: 2, // 保持原状态值
    tab: 'awaiting', // 添加tab参数标识当前标签
    ...search,
  }
  ContentInstance.get(data)
    .then(res => {
      awaitingList.value = processContentData(res.items || [])
      // 不在这里更新total，而是使用getTotalCounts获取的值
    })
    .finally(() => {
      awaitingLoading.value = false
    })
}

function getDraftList() {
  if (!model_id.value) return

  draftLoading.value = true
  const data = {
    model_id: model_id.value,
    limit: draftLimit.value,
    page: draftPage.value,
    status: 3, // 保持原状态值
    tab: 'draft', // 添加tab参数标识当前标签
    ...search,
  }
  ContentInstance.get(data)
    .then(res => {
      draftList.value = processContentData(res.items || [])
      // 不在这里更新total，而是使用getTotalCounts获取的值
    })
    .finally(() => {
      draftLoading.value = false
    })
}

// S 分页器
let approvedPage = ref(1)
let approvedLimit = ref(20)
let approvedTotal = ref(0)
const approvedChangePage = () => {
  getApprovedList()
}
let displayingPage = ref(1)
let displayingLimit = ref(20)
let displayingTotal = ref(0)
const displayingChangePage = () => {
  getDisplayingList()
}
let awaitingPage = ref(1)
let awaitingLimit = ref(20)
let awaitingTotal = ref(0)
const awaitingChangePage = () => {
  getAwaitingList()
}
let draftPage = ref(1)
let draftLimit = ref(20)
let draftTotal = ref(0)
const draftChangePage = () => {
  getDraftList()
}

// 搜索
const sortBy = ref('updated_at') // 排序字段
const sortOrder = ref('desc') // 排序方向
const search = reactive({
  id: '',
  title: '',
  data_lang: lang,
  category_id: undefined as number | undefined,
  sort_by: sortBy,
  sort_order: sortOrder
})
const searchContent = async () => {
  search.category_id = categoryList.value[0].id
  await changeLang(search.data_lang)
  await getTotalCounts() // 先获取最新的统计数据
  await getApprovedList()
  await getDisplayingList()
  await getAwaitingList()
  await getDraftList()
  showFilterDropdown.value = false
}
const refreshContent = () => {
  search.id = ''
  search.title = ''
  search.category_id = undefined

  approvedPage.value = 1
  displayingPage.value = 1
  awaitingPage.value = 1
  draftPage.value = 1

  showFilterDropdown.value = false

  getTotalCounts() // 刷新时获取最新的统计数据
  getApprovedList()
  getDisplayingList()
  getAwaitingList()
  getDraftList()
}

// 翻译
const targetLanguage = ref('')
const translateDialog = ref(false)
const translateContentHandle = (content: Content) => {
  translateDialog.value = true
  if (content) checkedContents.value = [{ ...content }]
}
const translateConfirm = () => {
  const content = checkedContents.value[0]
  if (content && content.id && content.lang) {
    ContentInstance.translate(content.id, content.lang, targetLanguage.value || '')
      .then(() => {
        translateDialog.value = false
      })
  }
}

// 语言切换
const changeLang = (_lang: string) => {
  if (lang == _lang) return
  lang = _lang
  localStorage.setItem('data_language', lang)
}

// 根据路由参数设置激活的标签
const setActiveTab = () => {
  const activeTab = route.query.active_tab as string || 'approved'

  if (activeTab && ['approved', 'displaying', 'awaiting', 'draft'].includes(activeTab)) {
    activeName.value = activeTab
    // 立即加载对应标签的数据
    handleClick(activeTab)
  }
}
// 页面初始化
const pageInit = async () => {
  model_id.value = Number(route.query.model_id) || 0
  await getCategoryList()
  await getTotalCounts() // 初始化时获取各标签的数量统计

  // 设置激活的标签
  setActiveTab()

  // 如果没有通过路由参数设置激活标签，则加载默认标签的数据
  if (!route.query.active_tab) {
    switch (activeName.value) {
      case 'approved':
        await getApprovedList()
        break
      case 'displaying':
        await getDisplayingList()
        break
      case 'awaiting':
        await getAwaitingList()
        break
      case 'draft':
        await getDraftList()
        break
    }
  }

  pageName.value = appStore.getPageName
}

watch(
  () => route.fullPath,
  () => {
    pageInit() // 路由参数变化后调用
  },
)

onMounted(() => {
  pageInit()
})

// 添加导入处理函数
const handleImport = () => {
  // 导入功能待实现
  console.log('导入功能待实现')
}

// 添加导出处理函数
const handleExport = () => {
  // 导出功能待实现
  console.log('导出功能待实现')
}

// 如果Content类型未在代码中定义，可以在script标签下添加：
interface Content {
  id: number;
  content_group_id?: number;
  category_id?: number;
  model_id?: number;
  alias?: string;
  title?: string;
  summary?: string;
  cover?: string | null;
  seo_title?: string | null;
  seo_description?: string | null;
  seo_keywords?: string | null;
  post_time?: string;
  word_count?: number | null;
  view_count?: number | null;
  status?: number;
  comment_count?: number | null;
  like_count?: number | null;
  is_recommend?: number | null;
  is_top?: number | null;
  tags?: string | null;
  author?: string;
  source?: string;
  verify_status?: number;
  detail_template?: string | null;
  lang?: string;
  creator_id?: number;
  created_at?: string;
  updated_at?: string;
  content?: string | null;
  content_id?: number;
  model_data_table_id?: number;
  category_name?: string;
  approval_text?: string;
  approval_date?: string;
  display_text?: string;
  delete_text?: string;
  new_page_text?: string;
  url?: string;
  price?: string;
  issuedate?: string;
  color?: string;
  stock_status?: string;
  sort?: number; // 排序值
}

// 在现有的响应式状态声明附近添加变量（可以搜索reactive和ref）
const showFilterDropdown = ref(false)

</script>

<style scoped lang="scss">
.bwms-module{
  .module-con {
    .box {
      padding-top: 0;
    }
  }
}

/* 自定义 tooltip 宽度样式 */
:deep(.el-tooltip__popper) {
  max-width: 300px !important;
  word-break: break-all;
}

/* 文本省略显示 */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

:deep(.el-select-dropdown__item) {
  text-align: center;
}

.content-info {
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  color: #707070;
  margin-top: 5px;

  span {
    margin-right: 10px;
    margin-bottom: 3px;
  }
}

.flex {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.approval-status {
  display: flex;
  flex-direction: column;
}

.approval-date {
  font-size: 12px;
  color: #707070;
  margin-top: 4px;
  white-space: normal; // 允许长文本换行
  line-height: 1.3; // 适当的行高
  word-break: break-all; // 允许在任何字符处换行
}

// 添加产品列表样式
.product-title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-title {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.product-price {
  color: #e65d0e;
  font-weight: 500;
  font-size: 13px;
  margin-bottom: 4px;
}

.product-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #707070;

  span {
    white-space: nowrap;
  }
}

.verify-status {
  font-size: 12px;
  color: #409EFF;
  margin-top: 4px;
}

// 优化图片预览
:deep(.el-image-viewer__wrapper) {
  .el-image-viewer__close {
    color: #fff;
  }
}

.bwms-btn {
  position: relative;

  /* 自定义loading样式 */
  &:deep(.el-loading-mask) {
    border-radius: 50%;
    /* 确保loading遮罩是圆形的 */
    background-color: rgba(255, 255, 255, 0.7);
    /* 稍微透明一点 */
  }
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 可选：当按钮处于loading状态时的样式 */
.bwms-btn:disabled {
  cursor: wait;
  background-color: #f5f7fa;
  border-color: #e4e7ed;

  .is-loading {
    color: #409EFF;
  }
}

// 添加排序相关样式
.el-input-number {
  width: 70px;
}

.sort-column {
  .cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>

<template>
  <div class="bwms-module table-page">
    <div class="module-header">
     
      <div class="btn-list">
        <el-button class="button-no-border" @click="handleImport">
          <el-icon><img src="/resources/admin/assets/icon/UploadIcon.png" alt="UploadIcon" /></el-icon>
          <span>{{ $t('Members.GroupList.import') }}</span>
        </el-button>
        <el-button class="button-no-border" @click="handleExport">
          <el-icon><img src="/resources/admin/assets/icon/DownloadIcon.png" alt="DownloadIcon" /></el-icon>
          <span>{{ $t('Members.GroupList.export') }}</span>
        </el-button>
        <!-- 筛选 -->
        <FilterPopover 
          v-model="showFilterDropdown"
        >
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchQuery" label-position="top" style="width: 100%;">
            <el-form-item :label="$t('Members.SubscriptionList.columns.planName')" style="width: 100%;">
              <el-input 
                v-model="searchQuery"
                :placeholder="$t('Members.SubscriptionList.searchPlaceholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreate">
          <el-icon><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
          <span>{{ $t('Members.SubscriptionList.createPlan') }}</span>
        </el-button>
      </div>
    </div>

    <div class="module-con">
      <div class="box">

        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="plan_name" :label="$t('Members.SubscriptionList.columns.planName')" min-width="120" />
          <el-table-column prop="price" :label="$t('Members.SubscriptionList.columns.price')" min-width="120">
            <template #default="{ row }">
              {{ row.currency }} {{ row.discount_price }}/{{ row.period_type === 'monthly' ? $t('Members.SubscriptionList.period.month') : $t('Members.SubscriptionList.period.year') }}
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="$t('Members.SubscriptionList.columns.status')" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="register_logs_count" :label="$t('Members.SubscriptionList.columns.subscriberCount')" width="120">
          </el-table-column>
          <el-table-column prop="updated_at" :label="$t('Members.SubscriptionList.columns.lastUpdate')" width="160" />
          <el-table-column :label="$t('Members.SubscriptionList.columns.actions')" width="200" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <duv class="bwms-operate-btn" @click="handleEdit(row)">
                  <el-icon><img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" /></el-icon>
                </duv>
                <duv class="bwms-operate-btn" @click="handleCopy(row)">
                  <el-icon><img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" /></el-icon>
                </duv>
                <duv class="bwms-operate-btn" @click="handleDelete(row)">
                  <el-icon><img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" /></el-icon>
                </duv>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="currentPage"
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      :title="$t('Members.SubscriptionList.importDialog.title')"
      width="500px"
      class="el-dialog-common-cls"
    >
      <div class="import-container">
        <el-upload
          class="upload-demo"
          drag
          action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            {{ $t('Members.GroupList.importDialog.dragText') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ $t('Members.SubscriptionList.importDialog.fileTip') }}
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <el-button type="primary" link @click="handleDownloadTemplate">
            {{ $t('Members.SubscriptionList.importDialog.downloadTemplate') }}
          </el-button>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="importDialogVisible = false">{{ $t('Members.SubscriptionList.importDialog.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedFile"
          >
            {{ $t('Members.SubscriptionList.importDialog.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Edit, Delete, More, CopyDocument, Download, Upload, Plus } from '@element-plus/icons-vue'
import { subscriptionService } from '../../services/subscriptionService'
import type { UploadInstance, UploadFile } from 'element-plus'
// @ts-ignore
import { env, getBaseUrl } from '/admin/support/helper'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

// 定义接口
interface Subscription {
  id: number
  plan_name: string
  currency: string
  original_price: string
  discount_price: string
  period_type: string
  period_days: number
  trial_days: number
  allow_discount: boolean
  allow_auto_renew: boolean
  description: string
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface ListResponse {
  total: number
  items: Subscription[]
}

interface ApiResponse {
  code: number
  message: string
  data: ListResponse
}

const router = useRouter()
const loading = ref(false)
const searchQuery = ref(undefined as any)
const tableData = ref<Subscription[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const baseURL = getBaseUrl()

// 添加上传相关的状态
const importDialogVisible = ref(false)
const uploadRef = ref<UploadInstance>()
const selectedFile = ref<UploadFile>()
const uploading = ref(false)

const showFilterDropdown = ref(false)

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      keyword: searchQuery.value,
      page: currentPage.value,
      limit: pageSize.value
    }
    const response = await subscriptionService.getList(params)
    
    // 确保响应数据符合预期格式
    if (response.data && response.data.code === 200 && response.data.data) {
      tableData.value = response.data.data.items || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(t('Members.SubscriptionList.errors.invalidDataFormat'))
    }
  } catch (error) {
    console.error('获取订阅方案列表失败:', error)
    ElMessage.error(t('Members.SubscriptionList.errors.fetchListFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  showFilterDropdown.value = false
  searchQuery.value = searchQuery.value ? searchQuery.value : undefined
  getList()
}
// 重置
const handleReset = () => {
  searchQuery.value = undefined
  currentPage.value = 1
  pageSize.value = 10
  showFilterDropdown.value = false
  getList()
}

// 分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getList()
}

// 新增
const handleCreate = () => {
  router.push('/members/subscription-plan/create')
}

// 编辑
const handleEdit = (row: Subscription) => {
  router.push(`/members/subscription-plan/${row.id}/edit`)
}

// 复制
const handleCopy = async (row: Subscription) => {
  try {
    await subscriptionService.copy(row.id)
    ElMessage.success(t('Members.SubscriptionList.success.copySuccess'))
    getList()
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error(t('Members.SubscriptionList.errors.copyFailed'))
  }
}

// 删除
const handleDelete = (row: Subscription) => {
  ElMessageBox.confirm(
    t('Members.SubscriptionList.deleteConfirm.message'), 
    t('Members.SubscriptionList.deleteConfirm.title'), 
    {
      confirmButtonText: t('Members.SubscriptionList.deleteConfirm.confirm'),
      cancelButtonText: t('Members.SubscriptionList.deleteConfirm.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await subscriptionService.delete(row.id)
      ElMessage.success(t('Members.SubscriptionList.success.deleteSuccess'))
      getList()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error(t('Members.SubscriptionList.errors.deleteFailed'))
    }
  })
}

// 状态变更
const handleStatusChange = async (row: Subscription) => {
  try {
    await subscriptionService.updateStatus(row.id, row.status)
    ElMessage.success(t('Members.SubscriptionList.success.updateSuccess'))
  } catch (error) {
    ElMessage.error(t('Members.SubscriptionList.errors.updateFailed'))
    row.status = row.status === 1 ? 0 : 1 // 恢复状态
  }
}

// 文件选择改变
const handleFileChange = (uploadFile: UploadFile) => {
  selectedFile.value = uploadFile
}

// 导入按钮点击
const handleImport = () => {
  importDialogVisible.value = true
  selectedFile.value = undefined
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 处理文件上传
const handleUpload = async () => {
  if (!selectedFile.value?.raw) {
    ElMessage.warning(t('Members.SubscriptionList.warnings.selectFile'))
    return
  }

  uploading.value = true
  try {
    await subscriptionService.import(selectedFile.value.raw)
    ElMessage.success(t('Members.SubscriptionList.success.importSuccess'))
    importDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    ElMessage.error(t('Members.SubscriptionList.errors.importFailed'))
  } finally {
    uploading.value = false
  }
}

// 下载模板
const handleDownloadTemplate = () => {
  try {
    loading.value = true
    const templateUrl = `${baseURL}members/subscription-plan/import-template`
    const link = document.createElement('a')
    link.href = templateUrl
    link.setAttribute('download', t('Members.SubscriptionList.templateFileName'))
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success(t('Members.SubscriptionList.success.templateDownloadSuccess'))
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error(t('Members.SubscriptionList.errors.templateDownloadFailed'))
  } finally {
    loading.value = false
  }
}

// 批量导出
const handleExport = async () => {
  try {
    loading.value = true
    const exportUrl = `${baseURL}members/subscription-plan/export?page=${currentPage.value}&limit=${pageSize.value}`
    const link = document.createElement('a')
    link.href = exportUrl
    link.setAttribute('download', t('Members.SubscriptionList.exportFileName'))
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success(t('Members.SubscriptionList.success.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('Members.SubscriptionList.errors.exportFailed'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 允许整个内容区域滚动

      .search-bar {
        margin-bottom: 20px;
        flex-shrink: 0; // 防止搜索区域被压缩
        
        .search-input {
          width: 300px;
        }
      }
    }
  }
}

.import-container {
  .template-download {
    margin-bottom: 16px;
    
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

</style> 
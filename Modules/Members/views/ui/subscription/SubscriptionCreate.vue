<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    
   
    </div>

    <div class="module-con">
      <div class="box">
        <el-tabs v-model="activeTab">
          <!-- 基本设置 -->
          <el-tab-pane :label="$t('Members.SubscriptionCreate.tabs.basicSettings')" name="basic">
            <el-form
              ref="basicFormRef"
              :model="form.basic"
              :rules="rules.basic"
              label-position="top"
              class="subscription-form"
            >
              <el-form-item :label="$t('Members.SubscriptionCreate.formItems.planName')" prop="plan_name" required>
                <el-input v-model="form.basic.plan_name" :placeholder="$t('Members.SubscriptionCreate.placeholders.enterPlanName')" />
              </el-form-item>

              <el-form-item :label="$t('Members.SubscriptionCreate.formItems.description')" prop="description">
                <el-input
                  v-model="form.basic.description"
                  type="textarea"
                  :rows="6"
                  :autosize="{ minRows: 6, maxRows: 10 }"
                  :placeholder="$t('Members.SubscriptionCreate.placeholders.descriptionExample')"
                />
              </el-form-item>

              <el-form-item label-position="left" :label="$t('Members.SubscriptionCreate.formItems.enablePlan')" prop="status">
                <el-switch
                style="margin-bottom: 8px;"
                  v-model="form.basic.status"
                  :active-value="1"
                  :inactive-value="0"
                />
              </el-form-item>
            </el-form>
            <div class="flex justify-center" style="margin-top: 26px;">
              <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.SubscriptionCreate.buttons.cancel') }}</el-button>
              <el-button type="primary" @click="handleNext">{{ $t('Members.SubscriptionCreate.buttons.next') }}</el-button>
            </div>
          </el-tab-pane>

          <!-- 价格设置 -->
          <el-tab-pane :label="$t('Members.SubscriptionCreate.tabs.priceSettings')" name="price">
            <el-form
              ref="priceFormRef"
              :model="form.price"
              :rules="rules.price"
              label-position="top"
              class="subscription-form"
            >
              <el-form-item :label="$t('Members.SubscriptionCreate.formItems.basicPriceSettings')" required class="price-section">
                <div class="price-row">
                  <el-form-item :label="$t('Members.SubscriptionCreate.formItems.currency')" prop="currency" class="sub-item">
                    <el-select v-model="form.price.currency" :placeholder="$t('Members.SubscriptionCreate.placeholders.pleaseSelect')">
                      <el-option label="HKD" value="HKD" />
                      <el-option label="USD" value="USD" />
                      <el-option label="CNY" value="CNY" />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('Members.SubscriptionCreate.formItems.originalPrice')" prop="original_price" class="sub-item">
                    <el-input v-model.number="form.price.original_price" :placeholder="$t('Members.SubscriptionCreate.placeholders.pleaseEnter')" />
                  </el-form-item>
                  <el-form-item :label="$t('Members.SubscriptionCreate.formItems.discountPrice')" prop="discount_price" class="sub-item">
                    <el-input v-model.number="form.price.discount_price" :placeholder="$t('Members.SubscriptionCreate.placeholders.pleaseEnter')" />
                  </el-form-item>
                </div>
              </el-form-item>

              <el-form-item :label="$t('Members.SubscriptionCreate.formItems.billingCycle')" required class="period-section">
                <div class="period-row">
                  <el-form-item :label="$t('Members.SubscriptionCreate.formItems.selectType')" prop="period_type" class="sub-item">
                    <el-select v-model="form.price.period_type" :placeholder="$t('Members.SubscriptionCreate.placeholders.pleaseSelect')">
                      <el-option :label="$t('Members.SubscriptionCreate.periodOptions.monthly')" value="monthly" />
                      <el-option :label="$t('Members.SubscriptionCreate.periodOptions.yearly')" value="yearly" />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('Members.SubscriptionCreate.formItems.trialDays')" prop="trial_days" class="sub-item">
                    <el-input v-model.number="form.price.trial_days" :placeholder="$t('Members.SubscriptionCreate.placeholders.pleaseEnter')" />
                  </el-form-item>
                </div>
              </el-form-item>

              <el-form-item>
                <el-checkbox v-model="form.price.allow_discount">
                  {{ $t('Members.SubscriptionCreate.formItems.enableFirstSubscriptionDiscount') }}
                </el-checkbox>
              </el-form-item>

              <el-form-item>
                <el-checkbox v-model="form.price.allow_auto_renew">
                  {{ $t('Members.SubscriptionCreate.formItems.enableAutoRenewal') }}
                </el-checkbox>
              </el-form-item>
            </el-form>
            <div class="flex justify-center" style="margin-top: 26px;">
              <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.SubscriptionCreate.buttons.cancel') }}</el-button>
              <el-button @click="activeTab = 'basic'">{{ $t('Members.SubscriptionCreate.buttons.previous') }}</el-button>
              <el-button type="primary" @click="handleSubmit" :loading="loading">
                {{ $t('Members.SubscriptionCreate.buttons.submit') }}
              </el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { subscriptionService } from '../../services/subscriptionService'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const router = useRouter()
const loading = ref(false)
const activeTab = ref('basic')
const basicFormRef = ref<FormInstance>()
const priceFormRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  basic: {
    plan_name: '',
    description: '',
    status: 1
  },
  price: {
    currency: 'HKD',
    original_price: undefined as number | undefined,
    discount_price: undefined as number | undefined,
    period_type: 'monthly',
    period_days: 30,
    trial_days: undefined as number | undefined,
    allow_discount: true,
    allow_auto_renew: true
  }
})

// 表单验证规则
const rules = computed<Record<string, FormRules>>(() => ({
  basic: {
    plan_name: [
      { required: true, message: t('Members.SubscriptionCreate.validations.planNameRequired'), trigger: 'blur' },
      { min: 2, max: 50, message: t('Members.SubscriptionCreate.validations.planNameLength'), trigger: 'blur' }
    ]
  },
  price: {
    currency: [
      { required: true, message: t('Members.SubscriptionCreate.validations.currencyRequired'), trigger: 'change' }
    ],
    original_price: [
      { required: true, message: t('Members.SubscriptionCreate.validations.originalPriceRequired'), trigger: 'blur' },
      { type: 'number', min: 0, message: t('Members.SubscriptionCreate.validations.pricePositive'), trigger: 'blur' }
    ],
    discount_price: [
      { required: true, message: t('Members.SubscriptionCreate.validations.discountPriceRequired'), trigger: 'blur' },
      { type: 'number', min: 0, message: t('Members.SubscriptionCreate.validations.pricePositive'), trigger: 'blur' }
    ],
    period_type: [
      { required: true, message: t('Members.SubscriptionCreate.validations.periodTypeRequired'), trigger: 'change' }
    ],
    trial_days: [
      { required: true, message: t('Members.SubscriptionCreate.validations.trialDaysRequired'), trigger: 'blur' },
      { type: 'number', min: 0, message: t('Members.SubscriptionCreate.validations.daysPositive'), trigger: 'blur' }
    ]
  }
}))

// 监听周期类型变化，自动设置周期天数
watch(() => form.price.period_type, (newValue) => {
  form.price.period_days = newValue === 'monthly' ? 30 : 365
})

// 取消
const handleCancel = () => {
  router.back()
}

// 提交
const handleSubmit = async () => {
  try {
    const [basicValid, priceValid] = await Promise.all([
      basicFormRef.value?.validate(),
      priceFormRef.value?.validate()
    ])

    if (basicValid && priceValid) {
      loading.value = true
      // 准备提交的数据
      const data = {
        ...form.basic,
        ...form.price
      }
      console.log('data-from', data);
      
      await subscriptionService.create(data)
      ElMessage.success(t('Members.SubscriptionCreate.success.createSuccess'))
      router.push('/members/subscription-plan')
    }
  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error(t('Members.SubscriptionCreate.errors.createFailed'))
  } finally {
    loading.value = false
  }
}

// 下一步
const handleNext = async () => {
  if (activeTab.value === 'basic') {
    await basicFormRef.value?.validate((valid) => {
      if (valid) {
        activeTab.value = 'price'
      }
    })
  }
}

</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 0;

      .subscription-form {
        .price-section, .period-section {
          .price-row, .period-row {
            display: flex;
            gap: 26px;
            margin-bottom: 26px;
            width: 100%;

            .sub-item {
              margin-bottom: 0;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style> 
<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    
    </div>

    <div class="module-con">
      <div class="box">
        <div class="form-section">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            class="group-form"
          >
            <el-form-item :label="$t('Members.GroupCreate.formItems.groupName')" prop="group_name" required>
              <el-input 
                v-model="form.group_name" 
                :placeholder="$t('Members.GroupCreate.placeholders.pleaseEnterGroupName')" 
                :maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <el-form-item :label="$t('Members.GroupCreate.formItems.groupDescription')" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                :maxlength="200"
                show-word-limit
                :placeholder="$t('Members.GroupCreate.placeholders.pleaseEnterGroupDescription')"
              />
            </el-form-item>

          </el-form>
        </div>
      </div>
    </div>
    <div class="flex justify-center" style="margin-top: 20px;">
      <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.GroupCreate.buttons.cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ $t('Members.GroupCreate.buttons.save') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { memberGroupService } from '../../services/memberGroupService'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = reactive({
  group_name: '',
  description: '',
  status: 1
})

// 表单验证规则 - 使用国际化
const rules = computed<FormRules>(() => ({
  group_name: [
    { required: true, message: t('Members.GroupCreate.validations.groupNameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Members.GroupCreate.validations.groupNameLength'), trigger: 'blur' }
  ],
  description: [
    { max: 200, message: t('Members.GroupCreate.validations.descriptionMaxLength'), trigger: 'blur' }
  ]
}))

// 取消
const handleCancel = () => {
  router.back()
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const submitData = {
          group_name: form.group_name,
          description: form.description,
          status: form.status
        }
        await memberGroupService.create(submitData)
        ElMessage.success(t('Members.GroupCreate.success.createSuccess'))
        router.push('/members/group')
      } catch (error) {
        ElMessage.error(t('Members.GroupCreate.errors.createFailed'))
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;

    }
  }
}
</style> 
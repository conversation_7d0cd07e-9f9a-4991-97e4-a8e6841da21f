<template>
  <div class="table-page bwms-module">
    <div class="module-header"></div>
    <div class="module-con">
      <div class="box scroll-bar-custom-transparent">
        <!-- 步骤条 -->
        <el-steps :active="currentStep" class="steps">
          <el-step :title="$t('Members.MemberTypeEdit.steps.basicProperties')" />
          <el-step :title="$t('Members.MemberTypeEdit.steps.customFields')" />
          <el-step :title="$t('Members.MemberTypeEdit.steps.permissionSettings')" />
        </el-steps>

        <!-- 表单内容 -->
        <div class="form-content">
          <!-- 基本属性 -->
          <member-type-basic
            v-if="currentStep === 0"
            v-model:form="formData.basic"
            :loading="loading"
            @next="handleNext"
            @submit="handleSubmit"
          />

          <!-- 自定义字段设置 -->
          <member-type-fields
            v-if="currentStep === 1"
            v-model:form="formData"
            :loading="loading"
            @prev="currentStep--"
            @next="handleNext"
            @submit="handleSubmit"
          />

          <!-- 相关规则限制设置 -->
          <member-type-rules
            v-if="currentStep === 2"
            v-model:form="formData"
            :resources="resources"
            :loading="loading"
            @prev="currentStep--"
            @submit="handleSubmit"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { memberTypeService } from '../../services/memberService'
import MemberTypeBasic from './components/MemberTypeBasic.vue'
import MemberTypeFields from './components/MemberTypeFields.vue'
import MemberTypeRules from './components/MemberTypeRules.vue'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const currentStep = ref(0)

// 添加资源相关的接口定义
interface Resource {
  id: number
  resource_key: string
  resource_name: string
  resource_type: string
  resource_type_name: string
  description: string
  status: number
  creator_id: number
  created_at: number
  updated_at: number
  deleted_at: number
}

// 在 setup 中添加资源列表状态
const resources = ref<Resource[]>([])

// 定义接口
interface CustomField {
  id: number
  name: string
  type: string
  label: string
  options: string[]
  is_required: boolean | number
  is_searchable: boolean | number
  creator_id: number
  created_at: string
  updated_at: string
}

interface Permission {
  id: number
  type_id: number
  resource_id: number
  access_type: number
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

interface MemberTypeDetail {
  id: number
  type_name: string
  description: string
  is_default: number
  status: number
  custom_fields: number[]
  creator_id: number
  created_at: string
  updated_at: string
  custom_field_details: CustomField[]
  permissions: Permission[]
}

// 表单数据
const formData = ref({
  basic: {
    type_name: '',
    description: '',
    status: 1,
    is_default: false
  },
  custom_fields: [] as CustomField[],
  permissions: [] as Permission[]
})

// 获取详情
const getDetail = async () => {
  const id = route.params.id as string
  if (!id) return

  loading.value = true
  try {
    const { data } = await memberTypeService.getDetail(Number(id))
    if (data.data) {
      const detail = data.data as MemberTypeDetail
      
      // 设置基本信息
      formData.value.basic = {
        type_name: detail.type_name,
        description: detail.description || '',
        status: detail.status,
        is_default: detail.is_default === 1
      }
      
      // 设置自定义字段
      formData.value.custom_fields = detail.custom_field_details.map(field => ({
        ...field,
        is_required: field.is_required === 1,
        is_searchable: field.is_searchable === 1
      }))
      
      // 设置权限
      formData.value.permissions = detail.permissions
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberTypeEdit.errors.fetchDetailFailed'))
  } finally {
    loading.value = false
  }
}

// 添加获取资源列表的方法
const fetchResources = async () => {
  try {
    loading.value = true
    const { data } = await memberTypeService.getResources()
    
    if (data.data && data.data.items) {
      resources.value = data.data.items
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberTypeEdit.errors.fetchResourcesFailed'))
  } finally {
    loading.value = false
  }
}

// 下一步
const handleNext = () => {
  currentStep.value++
}

// 提交
const handleSubmit = async (submitFormData?: any) => {
  loading.value = true
  try {
    const id = route.params.id as string
    
    // 使用传入的数据或构建默认提交数据
    const submitData = submitFormData || {
      type_name: formData.value.basic.type_name,
      description: formData.value.basic.description,
      status: formData.value.basic.status,
      is_default: formData.value.basic.is_default,
      custom_fields: formData.value.custom_fields.map(field => ({
        name: field.name,
        type: field.type,
        label: field.label,
        options: field.options,
        is_required: field.is_required ? 1 : 0,
        is_searchable: field.is_searchable ? 1 : 0
      })),
      permissions: formData.value.permissions.map(permission => ({
        access_type: permission.access_type,
        resource_id: permission.resource_id,
        status: permission.status
      }))
    }

    if (id) {
      await memberTypeService.update(Number(id), submitData)
    } else {
      await memberTypeService.create(submitData)
    }
    
    ElMessage.success(t('Members.MemberTypeEdit.success.saveSuccess'))
    router.push('/members/type')
  } catch (error) {
    ElMessage.error(t('Members.MemberTypeEdit.errors.saveFailed'))
  } finally {
    loading.value = false
  }
}

// 在初始化时调用
onMounted(() => {
  fetchResources()
  getDetail() // 如果是编辑页面
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;

      .steps {
        margin-bottom: 30px;
        color: #000;
      }
    }
  }
}
</style>

<template>
  <div class="table-page bwms-module">
    <div class="module-header"></div>
    <div class="module-con">
      <div class="box scroll-bar-custom-transparent">
        <!-- 步骤条 -->
        <el-steps :active="currentStep" class="steps">
          <el-step :title="$t('Members.MemberTypeCreate.steps.basicProperties')" />
          <el-step :title="$t('Members.MemberTypeCreate.steps.customFields')" />
          <el-step :title="$t('Members.MemberTypeCreate.steps.permissionSettings')" />
        </el-steps>

        <!-- 表单内容 -->
        <div class="form-content">
          <!-- 基本属性 -->
          <member-type-basic
            v-if="currentStep === 0"
            v-model:form="formData.basic"
            :loading="loading"
            @next="handleNext"
            @submit="handleSubmit"
          />

          <!-- 自定义字段设置 -->
          <member-type-fields
            v-if="currentStep === 1"
            v-model:form="formData"
            :loading="loading"
            @prev="currentStep--"
            @next="handleNext"
            @submit="handleSubmit"
          />

          <!-- 相关规则限制设置 -->
          <member-type-rules
            v-if="currentStep === 2"
            v-model:form="formData"
            :resources="resources"
            :loading="loading"
            @prev="currentStep--"
            @submit="handleSubmit"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { memberTypeService } from '../../services/memberService'
import MemberTypeBasic from './components/MemberTypeBasic.vue'
import MemberTypeFields from './components/MemberTypeFields.vue'
import MemberTypeRules from './components/MemberTypeRules.vue'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const router = useRouter()
const loading = ref(false)
const currentStep = ref(0)

// 表单数据
const formData = ref({
  basic: {
    type_name: '',
    description: '',
    status: 1,
    is_default: false
  },
  custom_fields: [],
  permissions: []
})

// 添加资源相关的接口定义
interface Resource {
  id: number
  resource_key: string
  resource_name: string
  resource_type: string
  resource_type_name: string
  description: string
  status: number
  creator_id: number
  created_at: number
  updated_at: number
  deleted_at: number
}

// 在 setup 中添加资源列表状态
const resources = ref<Resource[]>([])

// 添加获取资源列表的方法
const fetchResources = async () => {
  try {
    loading.value = true
    const { data } = await memberTypeService.getResources()
    
    if (data.data && data.data.items) {
      resources.value = data.data.items
    }
  } catch (error) {
    ElMessage.error(t('Members.MemberTypeCreate.errors.fetchResourcesFailed'))
  } finally {
    loading.value = false
  }
}

// 在初始化时调用
onMounted(() => {
  fetchResources()
})

// 下一步
const handleNext = () => {
  currentStep.value++
}

// 提交
const handleSubmit = async () => {
  loading.value = true
  try {
    // 准备提交数据
    const submitData = {
      type_name: formData.value.basic.type_name,
      description: formData.value.basic.description,
      status: formData.value.basic.status,
      is_default: formData.value.basic.is_default,
      custom_fields: formData.value.custom_fields.map(field => ({
        name: field.name,
        type: field.type,
        label: field.label,
        options: field.options || [],
        is_required: field.is_required,
        is_searchable: field.is_searchable
      })),
      permissions: formData.value.permissions.map(({ access_type, resource_id, status }) => ({
        access_type,
        resource_id,
        status
      }))
    }

    await memberTypeService.create(submitData)
    ElMessage.success(t('Members.MemberTypeCreate.success.createSuccess'))
    router.push('/members/type')
  } catch (error) {
    ElMessage.error(t('Members.MemberTypeCreate.errors.createFailed'))
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;

      .steps {
        margin-bottom: 30px;
        color: #000;
      }
    }
  }
}
</style> 
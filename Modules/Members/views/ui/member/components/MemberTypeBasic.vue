<template>
  <div class="member-type-basic">
    <el-form 
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      class="form-container"
    >
      <div class="form-left">
        <el-form-item 
          :label="$t('Members.MemberTypeBasic.formItems.typeName')" 
          prop="type_name"
          required
        >
          <el-input 
            v-model="form.type_name"
            :placeholder="$t('Members.MemberTypeBasic.placeholders.pleaseEnterTypeName')"
          />
        </el-form-item>

        <el-form-item 
          :label="$t('Members.MemberTypeBasic.formItems.typeDescription')" 
          prop="description"
        >
          <el-input 
            v-model="form.description"
            type="textarea"
            :rows="4"
            :placeholder="$t('Members.MemberTypeBasic.placeholders.pleaseEnterTypeDescription')"
          />
        </el-form-item>
      </div>

      <div class="form-right">
        <el-form-item :label="$t('Members.MemberTypeBasic.formItems.setAsDefault')">
          <el-switch v-model="form.is_default" />
        </el-form-item>
      </div>
    </el-form>
    <div class="flex justify-center">
      <el-button class="button-cancel" @click="handleCancel" :loading="loading">{{ $t('Members.MemberTypeBasic.buttons.cancel') }}</el-button>
      <el-button @click="handleNext" :loading="loading">
        {{ $t('Members.MemberTypeBasic.buttons.next') }}
      </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ $t('Members.MemberTypeBasic.buttons.submit') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, computed } from 'vue'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const router = useRouter()

const props = defineProps<{
  form: {
    type_name: string
    description: string
    is_default: boolean
  }
  loading?: boolean
}>()

const emit = defineEmits<{
  (e: 'next'): void
  (e: 'submit'): void
  (e: 'update:form', value: typeof props.form): void
}>()

const formRef = ref<FormInstance>()

// 表单验证规则 - 使用国际化
const rules = computed(() => ({
  type_name: [
    { required: true, message: t('Members.MemberTypeBasic.validations.typeNameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Members.MemberTypeBasic.validations.typeNameLength'), trigger: 'blur' }
  ]
}))

// 取消
const handleCancel = () => {
  router.push('/members/type')
}

// 下一步
const handleNext = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('next')
    }
  })
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit')
    }
  })
}
</script>

<style lang="scss" scoped>
.member-type-basic {
  .form-container {
    display: flex;
    gap: 26px;
    margin-bottom: 26px;

    .form-left {
      flex: 1;
    }

    .form-right {
      width: 50%;
    }
  }
  
}
</style> 
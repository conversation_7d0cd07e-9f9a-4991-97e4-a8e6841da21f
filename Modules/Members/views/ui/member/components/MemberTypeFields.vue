<template>
  <div class="member-type-fields" :key="key">
    <el-form 
      ref="formRef"
      :model="form"
      label-position="top"
    >
      <template v-if="form.custom_fields.length > 0">
        <div v-for="(field, index) in form.custom_fields" :key="index" class="field-item">
          <div class="field-header">
            <h3>{{ $t('Members.MemberTypeFields.field') }} {{ index + 1 }}</h3>
            <el-button 
              type="text" 
              link
              class="delete-btn"
              @click="removeField(index)"
            >
              <el-icon><Delete /></el-icon>
              <!-- {{ $t('Members.MemberTypeFields.buttons.delete') }} -->
            </el-button>
          </div>

          <el-form-item 
            :prop="`custom_fields.${index}.label`"
            :label="$t('Members.MemberTypeFields.formItems.fieldName')"
            :rules="{ required: true, message: $t('Members.MemberTypeFields.validations.fieldNameRequired'), trigger: 'blur' }"
          >
            <el-input 
              v-model="field.label"
              :placeholder="$t('Members.MemberTypeFields.placeholders.pleaseEnterFieldLabel')"
            />
          </el-form-item>

          <el-form-item 
            :prop="`custom_fields.${index}.name`"
            :label="$t('Members.MemberTypeFields.formItems.fieldIdentifier')"
            :rules="{ required: true, message: $t('Members.MemberTypeFields.validations.fieldIdentifierRequired'), trigger: 'blur' }"
          >
            <el-input 
              v-model="field.name"
              :placeholder="$t('Members.MemberTypeFields.placeholders.pleaseEnterFieldIdentifier')"
            />
          </el-form-item>

          <el-form-item 
            :prop="`custom_fields.${index}.type`"
            :label="$t('Members.MemberTypeFields.formItems.fieldType')"
            :rules="{ required: true, message: $t('Members.MemberTypeFields.validations.fieldTypeRequired'), trigger: 'change' }"
          >
            <el-select v-model="field.type" :placeholder="$t('Members.MemberTypeFields.placeholders.pleaseSelectFieldType')">
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.text')" value="text" />
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.textarea')" value="textarea" />
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.number')" value="number" />
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.date')" value="date" />
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.select')" value="select" />
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.radio')" value="radio" />
              <el-option :label="$t('Members.MemberTypeFields.fieldTypes.checkbox')" value="checkbox" />
            </el-select>
          </el-form-item>

          <el-form-item 
            v-if="['select', 'radio', 'checkbox'].includes(field.type)"
            :prop="`custom_fields.${index}.options`"
            :label="$t('Members.MemberTypeFields.formItems.options')"
          >
            <div v-for="(option, optionIndex) in field.options" :key="optionIndex" class="option-item">
              <el-input v-model="field.options[optionIndex]" :placeholder="$t('Members.MemberTypeFields.placeholders.pleaseEnterOptionValue')" />
              <el-button type="danger" link @click="removeOption(index, optionIndex)">
                {{ $t('Members.MemberTypeFields.buttons.delete') }}
              </el-button>
            </div>
            <el-button type="primary" link @click="addOption(index)">
              {{ $t('Members.MemberTypeFields.buttons.addOption') }}
            </el-button>
          </el-form-item>

          <el-form-item :prop="`custom_fields.${index}.is_required`" :label="$t('Members.MemberTypeFields.formItems.isRequired')">
            <el-switch v-model="field.is_required" />
          </el-form-item>

          <el-form-item v-if="['select'].includes(field.type)" :prop="`custom_fields.${index}.is_searchable`" :label="$t('Members.MemberTypeFields.formItems.isSearchable')">
            <el-switch v-model="field.is_searchable" />
          </el-form-item>
        </div>
      </template>
      <el-empty v-else :description="$t('Cms.list.no_data')" image-size="100px" />

      <div class="add-field">
        <el-button type="primary" link @click="addField">
          <el-icon><Plus /></el-icon>
          {{ $t('Members.MemberTypeFields.buttons.addField') }}
        </el-button>
      </div>
    </el-form>
    <div class="flex justify-center">
      <el-button @click="emit('prev')" :loading="loading">{{ $t('Members.MemberTypeFields.buttons.previous') }}</el-button>
      <el-button @click="handleNext" :loading="loading">
        {{ $t('Members.MemberTypeFields.buttons.next') }}
      </el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ $t('Members.MemberTypeFields.buttons.submit') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t, locale } = useI18n()

// 组件刷新标记
const key = ref(0)

// 监听语言变化
watch(() => locale.value, () => {
  // 强制刷新组件
  key.value++
  
  // 如果表单已经存在，重新验证
  if (formRef.value) {
    formRef.value.clearValidate()
    formRef.value.validate()
  }
})

interface CustomField {
  id?: number
  name: string
  type: string
  label: string
  options: string[]
  is_required: boolean
  is_searchable: boolean
  creator_id?: number
  created_at?: string
  updated_at?: string
}

const props = defineProps<{
  form: {
    custom_fields: CustomField[]
  }
  loading?: boolean
}>()

const emit = defineEmits<{
  (e: 'prev'): void
  (e: 'next'): void
  (e: 'submit'): void
  (e: 'update:form', value: typeof props.form): void
}>()

const formRef = ref<FormInstance>()

// 添加字段
const addField = () => {
  props.form.custom_fields.push({
    name: '',
    type: 'text',
    label: '',
    options: [],
    is_required: false,
    is_searchable: false
  })
}

// 删除字段
const removeField = (index: number) => {
  props.form.custom_fields.splice(index, 1)
}

// 添加选项
const addOption = (fieldIndex: number) => {
  if (!props.form.custom_fields[fieldIndex].options) {
    props.form.custom_fields[fieldIndex].options = []
  }
  props.form.custom_fields[fieldIndex].options.push('')
}

// 删除选项
const removeOption = (fieldIndex: number, optionIndex: number) => {
  props.form.custom_fields[fieldIndex].options.splice(optionIndex, 1)
}

// 下一步
const handleNext = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('next')
    }
  })
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit')
    }
  })
}
</script>

<style lang="scss" scoped>
.member-type-fields {
  .field-item {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;

    .field-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }
  }
  .delete-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #f56c6c;
    &:hover {
      background-color: #fef0f0;
    }
  }
  .option-item {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
  }

  .add-field {
    margin-bottom: 20px;
    text-align: center;
  }

  .form-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;

    .el-button {
      min-width: 100px;
    }
  }
}
</style> 
<template>
  <div class="member-type-rules">
    <el-form 
      ref="formRef"
      :model="form"
      label-position="top"
    >
      <!-- 可访问内容 -->
      <div class="section">
        <h3>{{ $t('Members.MemberTypeRules.accessibleContent') }}</h3>
        <div class="permission-grid">
          <div 
            v-for="(item, index) in form.permissions.filter(p => p.access_type === 1)" 
            :key="index"
            class="permission-row"
          >
            <el-select 
              v-model="item.resource_type" 
              :placeholder="$t('Members.MemberTypeRules.placeholders.selectResourceType')"
              style="width: 40%"
              @change="handleResourceTypeChange(item)"
            >
              <el-option 
                v-for="type in resourceTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value" 
              />
            </el-select>
            <el-select 
              v-model="item.resource_id" 
              :placeholder="$t('Members.MemberTypeRules.placeholders.selectResource')"
              style="width: 50%"
              :disabled="!item.resource_type"
            >
              <el-option 
                v-for="resource in getResourcesByType(item.resource_type || '')" 
                :key="resource.id" 
                :label="resource.resource_name" 
                :value="resource.id"
                :disabled="isResourceSelected(resource.id, item)"
              />
            </el-select>
            <el-button 
              class="delete-btn"
              type="text"
              @click="handleDeleteAccessible(index)"
            >
              <el-icon size="16"><Delete /></el-icon>
            </el-button>
          </div>
          <el-button 
            class="add-row-btn"
            @click="handleAddAccessible"
          >
            <el-icon size="16"><Plus /></el-icon>
            <span>{{ $t('Members.MemberTypeRules.buttons.addAccessibleContent') }}</span>
          </el-button>
        </div>
      </div>

      <!-- 不可访问内容 -->
      <div class="section">
        <h3>{{ $t('Members.MemberTypeRules.inaccessibleContent') }}</h3>
        <div class="permission-grid">
          <div 
            v-for="(item, index) in form.permissions.filter(p => p.access_type === 2)" 
            :key="index"
            class="permission-row"
          >
            <el-select 
              v-model="item.resource_id" 
              :placeholder="$t('Members.MemberTypeRules.placeholders.selectResource')"
              style="width: 50%"
            >
              <el-option 
                v-for="resource in resources" 
                :key="resource.id" 
                :label="resource.resource_name" 
                :value="resource.id"
                :disabled="isResourceSelected(resource.id, item)"
              />
            </el-select>
            <el-button 
              class="delete-btn"
              type="text"
              @click="handleDeleteInaccessible(index)"
            >
              <el-icon size="16"><Delete /></el-icon>
            </el-button>
          </div>
          <el-button 
            class="add-row-btn"
            @click="handleAddInaccessible"
          >
            <el-icon size="16"><Plus /></el-icon>
            <span>{{ $t('Members.MemberTypeRules.buttons.addInaccessibleContent') }}</span>
          </el-button>
        </div>
      </div>
    </el-form>
    <!-- 按钮组 -->
    <div class="flex justify-center">
      <el-button class="button-cancel" @click="handleCancel">{{ $t('Members.MemberTypeRules.buttons.cancel') }}</el-button>
      <el-button @click="handlePrev">{{ $t('Members.MemberTypeRules.buttons.previous') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ $t('Members.MemberTypeRules.buttons.saveAndFinish') }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, computed } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { memberTypeService } from '../../../services/memberService'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 资源相关数据
interface Resource {
  id: number
  resource_key: string
  resource_name: string
  resource_type: string
  resource_type_name: string
  description: string
  status: number
  creator_id: number
  created_at: number
  updated_at: number
  deleted_at: number
}

// 类型映射
interface TypeOption {
  value: string
  label: string
}

// 内容项
interface Permission {
  id?: number
  type_id?: number
  resource_id: number | undefined
  access_type: number
  status: number
  resource_type?: string // 临时字段
  creator_id?: number
  created_at?: string
  updated_at?: string
}

interface FormData {
  permissions: Permission[]
}

const props = defineProps<{
  form: FormData
  resources: Resource[]
  loading?: boolean
}>()

const emit = defineEmits<{
  (e: 'prev'): void
  (e: 'submit'): void
  (e: 'update:form', value: FormData): void
}>()

// 资源类型变更时清空已选资源ID
const handleResourceTypeChange = (item: Permission) => {
  item.resource_id = undefined
}

// 添加可访问内容
const handleAddAccessible = () => {
  const newForm = { ...props.form }
  newForm.permissions.push({
    access_type: 1,
    resource_id: undefined,
    resource_type: '',
    status: 1
  })
  emit('update:form', newForm)
}

// 删除可访问内容
const handleDeleteAccessible = (index: number) => {
  const accessiblePermissions = props.form.permissions.filter(p => p.access_type === 1)
  const permissionToRemove = accessiblePermissions[index]
  
  const newForm = { ...props.form }
  const permissionIndex = newForm.permissions.findIndex(p => 
    p === permissionToRemove // 使用对象引用比较确保删除正确的项
  )
  
  if (permissionIndex !== -1) {
    newForm.permissions.splice(permissionIndex, 1)
    emit('update:form', newForm)
  }
}

// 添加不可访问内容
const handleAddInaccessible = () => {
  const newForm = { ...props.form }
  newForm.permissions.push({
    access_type: 2,
    resource_id: undefined,
    resource_type: '',
    status: 1
  })
  emit('update:form', newForm)
}

// 删除不可访问内容
const handleDeleteInaccessible = (index: number) => {
  const inaccessiblePermissions = props.form.permissions.filter(p => p.access_type === 2)
  const permissionToRemove = inaccessiblePermissions[index]
  
  const newForm = { ...props.form }
  const permissionIndex = newForm.permissions.findIndex(p => 
    p === permissionToRemove // 使用对象引用比较确保删除正确的项
  )
  
  if (permissionIndex !== -1) {
    newForm.permissions.splice(permissionIndex, 1)
    emit('update:form', newForm)
  }
}

// 修改为计算属性
const resourceTypes = computed(() => {
  const typeMap = new Map<string, string>()
  props.resources.forEach(resource => {
    if (resource.resource_type && resource.resource_type_name) {
      typeMap.set(resource.resource_type, resource.resource_type_name)
    }
  })
  
  return Array.from(typeMap.entries()).map(([value, label]) => ({
    value,
    label
  }))
})

// 修改获取资源列表的方法
const getResourcesByType = (type: string) => {
  return props.resources.filter(resource => resource.resource_type === type)
}

// 检查资源是否已被选择（禁用已选择的资源）
const isResourceSelected = (resourceId: number, currentItem: Permission) => {
  return props.form.permissions.some(p => 
    p.resource_id === resourceId && p !== currentItem
  )
}

// 取消
const handleCancel = () => {
  router.push('/members/type')
}

// 上一步
const handlePrev = () => {
  emit('prev')
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  // 检查是否有未选择资源ID的内容
  const hasEmptyResourceId = props.form.permissions.some(p => !p.resource_id)
  
  if (hasEmptyResourceId) {
    ElMessage.error(t('Members.MemberTypeRules.errors.emptyResourceExists'))
    return
  }
  
  // 提交前移除临时字段 resource_type
  const cleanPermissions = props.form.permissions.map(({ access_type, resource_id, status }) => ({
    access_type,
    resource_id,
    status
  }))
  
  // 更新表单数据，但不包含临时字段
  props.form.permissions = cleanPermissions

  await formRef.value.validate((valid) => {
    if (valid) {
      emit('submit')
    }
  })
}

// 初始化
onMounted(() => {
  // 为现有内容项设置 resource_type
  if (props.form.permissions) {
    props.form.permissions.forEach(permission => {
      if (permission.resource_id) {
        const resource = props.resources.find(r => r.id === permission.resource_id)
        if (resource) {
          permission.resource_type = resource.resource_type
        }
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.member-type-rules {
  .section {
    margin-bottom: 40px;

    h3 {
      font-size: 16px;
      margin-bottom: 8px;
      font-weight: 500;
      color: #000;
    }
  }

  .permission-grid {
    .permission-row {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;
      align-items: center;

      .delete-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #f56c6c;
        &:hover {
          background-color: #fef0f0;
        }
      }
    }

    .add-row-btn {
      border-style: dashed;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;
      
      &:hover {
        background-color: #ecf5ff;
      }

      .el-icon {
        margin-right: 4px;
      }
    }
  }

  .form-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
    
    .el-button {
      min-width: 100px;
    }
  }
}
</style> 
<?php

namespace Modules\Pay\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Modules\Pay\Models\PayConfig;
use Modules\Pay\Enums\PayErrorCode;

class PayConfigV2Service
{
    /**
     * 获取支付配置列表
     *
     * @param string $lang
     * @return array
     */
    public function configList(string $lang): array
    {
        $configs = PayConfig::orderBy('created_at', 'desc')
            ->get()
            ->map(function ($item) {
                return $this->formatConfigItem($item);
            })
            ->toArray();

        return $configs;
    }

    /**
     * 获取单个支付配置详情
     *
     * @param int $id
     * @param string $lang
     * @return array|null
     */
    public function getConfigDetail(int $id, string $lang): ?array
    {
        $config = PayConfig::where('id', $id)
            ->first();

        if (!$config) {
            return null;
        }

        return $this->formatConfigItem($config);
    }

    /**
     * 格式化配置项
     *
     * @param PayConfig $config
     * @return array
     */
    private function formatConfigItem(PayConfig $config): array
    {
        $result = $config->toArray();
        
        // 处理配置字段（可能是JSON字符串）
        if (isset($result['config']) && is_string($result['config'])) {
            $result['config'] = json_decode($result['config'], true) ?? [];
        }
        
        // 添加默认头像
        if (!isset($result['avatar']) || empty($result['avatar'])) {
            $result['avatar'] = $this->getDefaultAvatar($config->if_code);
        }
        
        return $result;
    }

    /**
     * 根据接口代码获取默认头像
     *
     * @param string $ifCode
     * @return string
     */
    private function getDefaultAvatar(string $ifCode): string
    {
        $avatars = [
            'alipay' => '/admin/images/payment/alipay.png',
            'wechat' => '/admin/images/payment/wechat.png',
            'paypal' => '/admin/images/payment/paypal.png',
            'stripe' => '/admin/images/payment/stripe.png',
            'unionpay' => '/admin/images/payment/unionpay.png',
        ];

        return $avatars[$ifCode] ?? '/admin/images/payment/default.png';
    }

    /**
     * 保存支付配置
     *
     * @param array $data
     * @param int $id
     * @return array
     * @throws BizException
     */
    public function saveConfig(array $data, int $id): array
    {
        DB::beginTransaction();
        try {
            if ($id > 0) {
                // 更新
                $config = PayConfig::findOrFail($id);
                
                // 处理config字段
                if (isset($data['config']) && is_array($data['config'])) {
                    $data['config'] = json_encode($data['config']);
                }
                
                $config->fill($data);
                $config->save();
            } else {
                // 创建
                // 处理config字段
                if (isset($data['config']) && is_array($data['config'])) {
                    $data['config'] = json_encode($data['config']);
                }
                
                $config = new PayConfig($data);
                $config->save();
            }

            DB::commit();
            return $this->formatConfigItem($config);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BizException(PayErrorCode::OPERATION_FAILED, '保存配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除支付配置
     *
     * @param int $id
     * @return bool
     * @throws BizException
     */
    public function deleteConfig(int $id): bool
    {
        try {
            $config = PayConfig::findOrFail($id);
            return $config->delete();
        } catch (\Exception $e) {
            throw new BizException(PayErrorCode::OPERATION_FAILED, '删除配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置配置状态
     *
     * @param int $id
     * @param int $status
     * @return bool
     * @throws BizException
     */
    public function setStatus(int $id, int $status): bool
    {
        try {
            $config = PayConfig::findOrFail($id);
            $config->status = $status;
            return $config->save();
        } catch (\Exception $e) {
            throw new BizException(PayErrorCode::OPERATION_FAILED, '设置状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可用的支付接口代码列表
     *
     * @return array
     */
    public function getIfCodes(): array
    {
        // 这里可以从配置或数据库获取支付接口列表
        return [
            ['value' => 'alipay', 'label' => '支付宝接口'],
            ['value' => 'wechat', 'label' => '微信支付接口'],
            ['value' => 'paypal', 'label' => 'PayPal接口'],
            ['value' => 'stripe', 'label' => 'Stripe接口'],
            ['value' => 'unionpay', 'label' => '银联支付接口']
        ];
    }
}

<?php
// Services/Adapters/ReceiptAdapter.php
namespace Modules\Pay\Services\Adapters;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Pay\Enums\OrderStatusEnum;
use Modules\Pay\Enums\PayErrorCode;
use Modules\Pay\Models\PayOrder;

/**
 * 收据支付适配器
 * 
 * 用于处理需要展示二维码并上传支付凭证的场景，如FPS、PayMe等
 */
class ReceiptAdapter extends AbstractAdapter
{
    /**
     * QR码存储路径
     */
    protected $qrCodePath = 'qrcodes';
    
    /**
     * 收据存储路径
     */
    protected $receiptPath = 'receipts';
    
    /**
     * 统一下单
     * 
     * 这里实现简单的订单创建，不涉及实际支付接口调用
     */
    public function unifiedOrder(array $orderData): array
    {
        try {
            $this->log('Receipt payment start', ['order_data' => $orderData]);
            
            // 获取支付二维码
            $qrCodeResult = $this->generateQrCode($orderData);
            
            // 如果获取二维码失败，直接返回错误
            if (!$qrCodeResult['success']) {
                return $qrCodeResult;
            }
            
            // 生成订单号作为标识
            $channelOrderNo = 'RECEIPT_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
            
            // 返回订单信息
            return [
                'success' => true,
                'channel_order_no' => $channelOrderNo,
                'pay_url' => '', // 无需支付URL
                'qrcode_url' => $qrCodeResult['qrcode_url'] ?? '',
                'qrcode_base64' => $qrCodeResult['qrcode_base64'] ?? '',
                'pay_data' => [
                    'channel_order_no' => $channelOrderNo,
                    'qr_type' => $qrCodeResult['qr_type'] ?? $orderData['qr_type'] ?? 'fps',
                    'amount' => $orderData['amount'],
                    'currency' => $orderData['currency'] ?? 'HKD',
                    'created_at' => date('Y-m-d H:i:s'),
                    'instructions' => $qrCodeResult['instructions'] ?? '',
                    'qrcode_url' => $qrCodeResult['qrcode_url']?? '',
                ],
            ];
            
        } catch (\Exception $e) {
            $this->logError('Receipt payment error', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取支付二维码
     * 
     * 从配置中获取已上传的支付二维码
     */
    public function generateQrCode(array $orderData): array
    {
        try {
            $this->log('Get QR code', ['order_data' => $orderData]);
            
            $qrCodeUrl = $this->configParams['qrcode_url'] ?? null;
            // 检查是否有二维码数据或URL
            if (empty($qrCodeUrl)) {
                $this->logError('QR code not configured', $this->configParams);
                return [
                    'success' => false,
                    'error_msg' => '未配置支付二维码，请在后台配置'
                ];
            }
            // 获取支付说明
            $qrCodeType = $orderData['qr_type'] ?? '';
            
            // 根据支付类型设置固定的说明文本
            $instructions = match($qrCodeType) {
                'fps' => '请使用香港FPS扫描二维码付款，付款后请上传付款截图',
                'payme' => '请使用PayMe扫描二维码付款，付款后请上传付款截图',
                default => '请扫描二维码付款，付款后请上传付款截图'
            };
            // 返回二维码数据
            return [
                'success' => true,
                'qrcode_url' => $qrCodeUrl,
                'qr_type' => $qrCodeType,
                'instructions' => $instructions
            ];
            
        } catch (\Exception $e) {
            $this->logError('Get QR code error', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 通过收据确认支付
     */
    public function confirmByReceipt(string $orderNo, $receiptFile, array $extraData = []): array
    {
        try {
            $this->log('Receipt confirmation start', [
                'order_no' => $orderNo,
                'extra_data' => $extraData
            ]);
            
            // 查找订单
            $order = PayOrder::where('pay_order_id', $orderNo)->first();
            if (!$order) {
                throw new \Exception('订单不存在');
            }
            
            // 检查订单状态
            if ($order->state == OrderStatusEnum::SUCCESS) {
                return [
                    'success' => true,
                    'message' => '订单已支付成功',
                    'order_no' => $orderNo,
                    'status' => OrderStatusEnum::SUCCESS
                ];
            }
            
            // 处理收据文件
            $receiptUrl = $this->saveReceiptFile($orderNo, $receiptFile);
            
            // 记录收据信息到订单附加信息中
            $channelExtra = is_string($order->channel_extra) 
                ? json_decode($order->channel_extra, true) 
                : ($order->channel_extra ?? []);
            
            $channelExtra = is_array($channelExtra) ? $channelExtra : [];
            $channelExtra['receipt_url'] = $receiptUrl;
            $channelExtra['receipt_time'] = date('Y-m-d H:i:s');
            $channelExtra['confirm_amount'] = $extraData['amount'] ?? $order->amount;
            $channelExtra['confirm_by'] = $extraData['user_id'] ?? 'system';
            $channelExtra['confirm_remark'] = $extraData['remark'] ?? '';
            
            // 更新订单状态为成功
            $order->update([
                'state' => OrderStatusEnum::SUCCESS,
                'channel_extra' => $channelExtra
            ]);
            
            // 返回成功结果
            return [
                'success' => true,
                'message' => '支付确认成功',
                'order_no' => $orderNo,
                'status' => OrderStatusEnum::SUCCESS,
                'receipt_url' => $receiptUrl
            ];
            
        } catch (\Exception $e) {
            $this->logError('Receipt confirmation error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 查询订单
     */
    public function queryOrder(string $orderNo): array
    {
        try {
            // 从数据库查询订单
            $order = PayOrder::where('pay_order_id', $orderNo)->first();
            
            if (!$order) {
                throw new \Exception("订单不存在: {$orderNo}");
            }
            
            // 解析channel_extra数据
            $channelExtra = is_string($order->channel_extra) 
                ? json_decode($order->channel_extra, true) 
                : ($order->channel_extra ?? []);
            
            // 返回订单状态
            return [
                'success' => true,
                'channel_order_no' => $order->channel_order_no,
                'status' => $order->state,
                'pay_data' => [
                    'receipt_url' => $channelExtra['receipt_url'] ?? null,
                    'receipt_time' => $channelExtra['receipt_time'] ?? null,
                    'confirm_amount' => $channelExtra['confirm_amount'] ?? $order->amount,
                    'confirm_by' => $channelExtra['confirm_by'] ?? 'system',
                    'confirm_remark' => $channelExtra['confirm_remark'] ?? ''
                ],
            ];
            
        } catch (\Exception $e) {
            $this->logError('Receipt query error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 关闭订单
     */
    public function closeOrder(string $orderNo): array
    {
        try {
            // 从数据库查询订单
            $order = PayOrder::where('pay_order_id', $orderNo)->first();
            
            if (!$order) {
                throw new \Exception("订单不存在: {$orderNo}");
            }
            
            // 检查订单状态
            if ($order->state == OrderStatusEnum::SUCCESS) {
                throw new \Exception("订单已支付成功，无法关闭");
            }
            
            // 更新订单状态为关闭
            $order->update([
                'state' => OrderStatusEnum::CLOSED
            ]);
            
            return [
                'success' => true,
                'channel_order_no' => $order->channel_order_no,
                'status' => OrderStatusEnum::CLOSED,
            ];
            
        } catch (\Exception $e) {
            $this->logError('Receipt close error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 申请退款
     * 
     * 由于是人工确认的支付，退款也需要人工处理
     */
    public function refund(array $refundData): array
    {
        try {
            $this->log('Receipt refund start', ['refund_data' => $refundData]);
            
            // 生成退款单号
            $refundNo = 'RF_' . $refundData['refund_order_id'];
            
            // 标记退款请求已创建，但需要人工处理
            return [
                'success' => true,
                'channel_refund_no' => $refundNo,
                'status' => OrderStatusEnum::PROCESSING,
                'refund_data' => [
                    'refund_amount' => $refundData['refund_amount'],
                    'remark' => '请人工处理此退款请求',
                    'created_at' => date('Y-m-d H:i:s')
                ],
            ];
            
        } catch (\Exception $e) {
            $this->logError('Receipt refund error', [
                'error' => $e->getMessage(),
                'refund_data' => $refundData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 查询退款
     */
    public function queryRefund(string $refundNo): array
    {
        // 退款状态需要人工确认，这里只返回处理中状态
        return [
            'success' => true,
            'channel_refund_no' => $refundNo,
            'status' => OrderStatusEnum::PROCESSING,
            'refund_data' => [
                'remark' => '请人工确认退款状态'
            ],
        ];
    }
    
    /**
     * 处理回调通知
     * 
     * 对于收据上传支付方式，不存在第三方回调
     */
    public function handleNotify(array $requestData): array
    {
        return [
            'verified' => true,
            'success' => false,
            'message' => '该支付方式不支持回调通知'
        ];
    }
    
    /**
     * 保存收据文件
     * 
     * @param string $orderNo 订单号
     * @param mixed $file 文件对象（UploadedFile或文件路径）
     * @return string 保存后的文件URL
     */
    protected function saveReceiptFile(string $orderNo, $file): string
    {
        // 定义保存路径
        $savePath = $this->receiptPath . '/' . date('Ymd');
        
        // 生成唯一文件名
        $filename = $orderNo . '_' . Str::random(8);
        
        // 处理不同类型的文件输入
        if ($file instanceof UploadedFile) {
            // 如果是上传的文件对象
            $extension = $file->getClientOriginalExtension();
            $fullPath = $file->storeAs($savePath, $filename . '.' . $extension, 'public');
        } elseif (is_string($file) && file_exists($file)) {
            // 如果是文件路径
            $extension = pathinfo($file, PATHINFO_EXTENSION);
            $fullPath = Storage::disk('public')->putFileAs(
                $savePath, 
                $file, 
                $filename . '.' . $extension
            );
        } elseif (preg_match('/^data:image\/(\w+);base64,/', $file, $matches)) {
            // 如果是base64编码的图片
            $extension = $matches[1];
            $base64Data = substr($file, strpos($file, ',') + 1);
            $decodedData = base64_decode($base64Data);
            
            $fullPath = $savePath . '/' . $filename . '.' . $extension;
            Storage::disk('public')->put($fullPath, $decodedData);
        } else {
            throw new \Exception('不支持的文件格式');
        }
        
        // 返回公共URL
        return asset('storage/' . $fullPath);
    }
    
    /**
     * 签名生成
     */
    protected function generateSign(array $params): string
    {
        // 收据上传方式不需要签名
        return '';
    }
    
    /**
     * 验证签名
     */
    protected function verifySign(array $params, string $sign): bool
    {
        // 收据上传方式不需要验证签名
        return true;
    }
} 
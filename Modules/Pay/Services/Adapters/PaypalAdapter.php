<?php
// Services/Adapters/PaypalAdapter.php
namespace Modules\Pay\Services\Adapters;
use Modules\Pay\Enums\OrderStatusEnum;
use Modules\Pay\Enums\RefundStatusEnum;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Modules\Pay\Models\PayConfig;

class PaypalAdapter extends AbstractAdapter
{
    //https://developer.paypal.com/dashboard/ 沙盒
    // 测试卡片
    //     Card number:****************
   //     Expiry date:Any
   // CVC code:Any
//    <EMAIL>
//    biWik-0M


// Sandbox URL：https://sandbox.paypal.com

// Email：<EMAIL>

// Password：biWik-0M

    /**
     * PayPal API基础URL
     */
    private string $apiBaseUrl;
    
    /**
     * PayPal访问令牌
     */
    private ?string $accessToken = null;
    
    /**
     * 令牌过期时间
     */
    private ?int $tokenExpires = null;
    
    /**
     * 构造函数
     */
    public function __construct(PayConfig $config)
    {
        parent::__construct($config);

        // 设置API基础URL（沙盒或生产环境）
        $isSandbox = ($this->configParams['mode'] ?? 'sandbox') === 'sandbox';
        $this->apiBaseUrl = $isSandbox 
            ? 'https://api-m.sandbox.paypal.com/' 
            : 'https://api-m.paypal.com/';
    }
    
    /**
     * 统一下单
     */
    public function unifiedOrder(array $orderData): array
    {
        try {
            $this->logApiRequest('unifiedOrder', $orderData);
            
            // 1. 构建PayPal请求参数
            $requestParams = $this->buildPaypalOrderParams($orderData);
            // 2. 调用PayPal API创建订单
            $response = $this->callPaypalApi('v2/checkout/orders', $requestParams, 'POST');
            
            // 3. 解析响应结果
            if (!isset($response['id'])) {
                throw new \Exception('PayPal create order failed: ' . json_encode($response));
            }
            
            // 4. 返回支付链接
            $approveLink = null;
            foreach ($response['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approveLink = $link['href'];
                    break;
                }
            }
            
            if (!$approveLink) {
                throw new \Exception('PayPal approve URL not found');
            }
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'],
                'pay_url' => $approveLink,
                'pay_data' => $response,
            ];
            
        } catch (\Exception $e) {
            $this->logError('PayPal unifiedOrder error', [
                'error' => $e->getMessage(),
                'data' => $orderData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 查询订单
     */
    public function queryOrder(string $orderNo): array
    {
        try {
            $this->logApiRequest('queryOrder', ['order_no' => $orderNo]);
            
            // 调用PayPal API查询订单
            $response = $this->callPaypalApi("v2/checkout/orders/{$orderNo}", [], 'GET');
            
            if (!isset($response['id'])) {
                throw new \Exception('PayPal query order failed: ' . json_encode($response));
            }
            
            // 解析订单状态
            $status = $this->mapPaypalStatus($response['status']);
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'],
                'status' => $status,
                'pay_data' => $response,
            ];
            
        } catch (\Exception $e) {
            $this->logError('PayPal queryOrder error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 关闭订单
     */
    public function closeOrder(string $orderNo): array
    {
        try {
            $this->logApiRequest('closeOrder', ['order_no' => $orderNo]);
            
            // PayPal没有直接的关闭订单API，但可以取消未授权或未捕获的订单
            $response = $this->callPaypalApi("v2/checkout/orders/{$orderNo}/void", [], 'POST');
            
            if (!isset($response['id'])) {
                throw new \Exception('PayPal close order failed: ' . json_encode($response));
            }
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'],
                'status' => OrderStatusEnum::CLOSED,
                'pay_data' => $response,
            ];
            
        } catch (\Exception $e) {
            $this->logError('PayPal closeOrder error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 申请退款
     */
    public function refund(array $refundData): array
    {
        try {
            $this->logApiRequest('refund', $refundData);
            
            // 获取捕获ID
            $captureId = null;
            
            // 1. 首先检查是否直接传入了capture_id
            if (!empty($refundData['capture_id'])) {
                $captureId = $refundData['capture_id'];
            } 
            // 2. 检查channel_extra中是否存在capture_id
            elseif (isset($refundData['channel_extra'])) {
                if (is_string($refundData['channel_extra'])) {
                    $extraData = json_decode($refundData['channel_extra'], true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($extraData['capture_id'])) {
                        $captureId = $extraData['capture_id'];
                    }
                } elseif (is_array($refundData['channel_extra']) && isset($refundData['channel_extra']['capture_id'])) {
                    $captureId = $refundData['channel_extra']['capture_id'];
                }
            }
            
            // 如果没有找到capture_id，使用错误提示
            if (empty($captureId)) {
                throw new \Exception('未找到PayPal捕获ID，无法退款。请确保支付成功并保存了capture_id。');
            }
            
            // 格式化退款金额（PayPal使用小数点表示金额）
            $refundAmount = number_format($refundData['refund_amount'] / 100, 2, '.', '');
            
            // 构建退款请求参数
            $requestParams = [
                'amount' => [
                    'value' => $refundAmount,
                    'currency_code' => $refundData['currency'] ?? 'HKD'
                ],
                'invoice_id' => $refundData['refund_order_id'] ?? '',
                'custom_id' => $refundData['mch_order_no'] ?? '', // 使用商户订单号作为自定义ID
                'note_to_payer' => $refundData['refund_reason'] ?? 'Refund request'
            ];
            
            $this->logApiRequest('发起退款请求', [
                'capture_id' => $captureId,
                'request_params' => $requestParams
            ]);
            
            // 调用PayPal退款API
            $response = $this->callPaypalApi("v2/payments/captures/{$captureId}/refund", $requestParams, 'POST');
            
            if (!isset($response['id'])) {
                throw new \Exception('PayPal退款失败: ' . json_encode($response));
            }
            
            return [
                'success' => true,
                'channel_refund_no' => $response['id'],
                'status' => $this->mapPaypalRefundStatus($response['status'] ?? ''),
                'refund_data' => $response,
                'amount' => [
                    'currency' => $response['amount']['currency_code'] ?? '',
                    'value' => $response['amount']['value'] ?? ''
                ],
                'create_time' => $response['create_time'] ?? '',
                'update_time' => $response['update_time'] ?? '',
                'custom_id' => $response['custom_id'] ?? '',
                'invoice_id' => $response['invoice_id'] ?? ''
            ];
            
        } catch (\Exception $e) {
            $this->logError('PayPal退款错误', [
                'error' => $e->getMessage(),
                'data' => $refundData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 退款查询
     */
    public function queryRefund(string $refundNo): array
    {
        try {
            $this->logApiRequest('queryRefund', ['refund_no' => $refundNo]);
            
            // 调用PayPal API查询退款
            $response = $this->callPaypalApi("v2/payments/refunds/{$refundNo}", [], 'GET');
            
            if (!isset($response['id'])) {
                throw new \Exception('PayPal query refund failed: ' . json_encode($response));
            }
            
            // 解析退款状态
            $status = $this->mapPaypalRefundStatus($response['status'] ?? '');
            
            return [
                'success' => true,
                'channel_refund_no' => $response['id'],
                'status' => $status,
                'refund_data' => $response,
                'amount' => [
                    'currency' => $response['amount']['currency_code'] ?? '',
                    'value' => $response['amount']['value'] ?? ''
                ],
                'create_time' => $response['create_time'] ?? '',
                'update_time' => $response['update_time'] ?? '',
                'custom_id' => $response['custom_id'] ?? '',
                'invoice_id' => $response['invoice_id'] ?? ''
            ];
            
        } catch (\Exception $e) {
            $this->logError('PayPal queryRefund error', [
                'error' => $e->getMessage(),
                'refund_no' => $refundNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 处理回调
     */
    public function handleNotify(array $requestData): array
    {
        try {
            $this->logApiRequest('handleNotify', $requestData);
            
            // 1. 获取事件类型和资源信息
            $eventType = $requestData['event_type'] ?? '';
            $resource = $requestData['resource'] ?? [];
            
            // 2. 根据事件类型处理不同的通知
            switch ($eventType) {
                case 'PAYMENT.CAPTURE.COMPLETED':
                    // 支付捕获完成
                    if (empty($resource) || !isset($resource['id'])) {
                        throw new \Exception('Invalid payment capture data');
                    }
                    
                    // 从自定义ID获取商户订单号
                    $customId = $resource['custom_id'] ?? $resource['invoice_id'] ?? '';
                    
                    // 如果找不到自定义ID，尝试获取订单ID并查询详情
                    if (empty($customId) && isset($resource['links'])) {
                        foreach ($resource['links'] as $link) {
                            if ($link['rel'] === 'up' && strpos($link['href'], '/orders/') !== false) {
                                $orderId = basename($link['href']);
                                $orderDetails = $this->callPaypalApi("v2/checkout/orders/{$orderId}", [], 'GET');
                                $customId = $orderDetails['purchase_units'][0]['custom_id'] ?? '';
                                break;
                            }
                        }
                    }
                    
                    if (empty($customId)) {
                        throw new \Exception('Cannot find merchant order ID from PayPal notification');
                    }
                    
                    return [
                        'verified' => true,
                        'success' => true,
                        'pay_order_id' => $customId,
                        'channel_order_no' => $resource['id'],
                        'amount' => ($resource['amount']['value'] ?? 0) * 100, // 转换为分
                        'status' => OrderStatusEnum::SUCCESS,
                    ];
                    
                case 'PAYMENT.CAPTURE.DENIED':
                case 'PAYMENT.CAPTURE.DECLINED':
                    // 支付被拒绝
                    return [
                        'verified' => true,
                        'success' => false,
                        'pay_order_id' => $resource['custom_id'] ?? '',
                        'channel_order_no' => $resource['id'] ?? '',
                        'status' => OrderStatusEnum::FAILED,
                        'message' => 'Payment was denied or declined'
                    ];
                    
                default:
                    return [
                        'verified' => true,
                        'success' => false,
                        'message' => 'Unsupported event type: ' . $eventType
                    ];
            }
            
        } catch (\Exception $e) {
            $this->logError('PayPal handleNotify error', [
                'error' => $e->getMessage(),
                'data' => $requestData
            ]);
            
            return [
                'verified' => false,
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 签名生成
     */
    protected function generateSign(array $params): string
    {
        // PayPal不需要生成签名，返回空
        return '';
    }
    
    /**
     * 签名验证
     */
    protected function verifySign(array $params, string $sign): bool
    {
        // PayPal不需要验证签名，使用Webhook验证
        return true;
    }
    
    /**
     * 构建PayPal订单参数
     */
    private function buildPaypalOrderParams(array $orderData): array
    {
        // 格式化金额（PayPal使用小数点表示金额）
        $amount = number_format($orderData['amount'] / 100, 2, '.', '');
        
        // 绝对最简化的请求格式，仅包含PayPal文档中必须的字段
        return [
            "intent" => "CAPTURE",
            "purchase_units" => [
                [
                    "amount" => [
                        "currency_code" => $orderData['currency'] ?? 'HKD',
                        "value" => $amount
                    ]
                ]
            ],
            "application_context" => [
                "return_url" => $this->configParams['return_url'] 
                    ?? (env('APP_URL') ? rtrim(env('APP_URL'), '/') . '/payment' : null),
                "cancel_url" => ($this->configParams['return_url'] 
                    ?? (env('APP_URL') ? rtrim(env('APP_URL'), '/') . '/payment' : null)) . '?status=cancel'
            ]
        ];
    }
    
    /**
     * 调用PayPal API
     */
    private function callPaypalApi(string $endpoint, array $data = [], string $method = 'POST'): array
    {
        try {
            // 1. 确保有访问令牌
            $this->ensureAccessToken();
            
            // 2. 准备请求参数
            $client = new Client([
                'http_errors' => false,  // 不要自动抛出HTTP错误，让我们能手动处理响应
                'verify' => true,        // 验证SSL证书
                'timeout' => 30          // 设置超时时间为30秒
            ]);
            
            $url = $this->apiBaseUrl . ltrim($endpoint, '/');
            
            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->accessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'PayPal-Request-Id' => uniqid('paypal-', true),
                ],
            ];
            
            // 添加请求体（如果不是GET请求）
            if ($method !== 'GET' && !empty($data)) {
                $options['json'] = $data;
            }
            
            // 记录详细的请求信息
            $this->logError('PayPal API Request (Debug)', [
                'endpoint' => $url,
                'method' => $method,
                'data' => json_encode($data, JSON_PRETTY_PRINT),
                'headers' => json_encode($options['headers'], JSON_PRETTY_PRINT)
            ]);
            
            // 3. 发送请求
            $response = $client->request($method, $url, $options);
            
            // 4. 处理响应
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            
            // 记录详细的响应信息
            $this->logError('PayPal API Response (Debug)', [
                'status_code' => $statusCode,
                'body' => $body
            ]);
            
            // 5. 解析JSON响应
            $result = json_decode($body, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Failed to parse PayPal API response: ' . json_last_error_msg());
            }
            
            // 检查HTTP状态码
            if ($statusCode >= 400) {
                $errorMessage = $result['message'] ?? 'Unknown PayPal error';
                $debugId = $result['debug_id'] ?? 'No debug ID';
                
                // 输出详细的错误信息
                $this->logError('PayPal API Error Details', [
                    'status_code' => $statusCode,
                    'error_message' => $errorMessage,
                    'debug_id' => $debugId,
                    'full_response' => json_encode($result, JSON_PRETTY_PRINT)
                ]);
                
                throw new \Exception("PayPal API error ($statusCode): $errorMessage [Debug ID: $debugId]");
            }
            
            return $result;
            
        } catch (GuzzleException $e) {
            // GuzzleHttp异常处理
            $errorMessage = $e->getMessage();
            
            // 记录错误
            $this->logError('PayPal API call failed', [
                'endpoint' => $endpoint,
                'error' => $errorMessage,
                'request_data' => json_encode($data, JSON_PRETTY_PRINT)
            ]);
            
            throw new \Exception('PayPal API request failed: ' . $errorMessage);
        }
    }
    
    /**
     * 确保获取有效的访问令牌
     */
    private function ensureAccessToken(): void
    {
        // 如果令牌有效，则直接返回
        if ($this->accessToken && $this->tokenExpires && time() < $this->tokenExpires - 60) {
            return;
        }
        
        try {
            // 获取客户端ID和密钥
            $clientId = $this->configParams['client_id'] ?? '';
            $clientSecret = $this->configParams['client_secret'] ?? '';
            
            if (empty($clientId) || empty($clientSecret)) {
                throw new \Exception('PayPal client ID or secret is not configured');
            }
            
            // 请求新令牌
            $client = new Client();
            $response = $client->request('POST', $this->apiBaseUrl . 'v1/oauth2/token', [
                'auth' => [$clientId, $clientSecret],
                'form_params' => [
                    'grant_type' => 'client_credentials',
                ],
                'headers' => [
                    'Accept' => 'application/json',
                    'Accept-Language' => 'en_US',
                ],
            ]);
            
            $data = json_decode($response->getBody()->getContents(), true);
            
            if (!isset($data['access_token']) || !isset($data['expires_in'])) {
                throw new \Exception('Invalid PayPal token response: ' . json_encode($data));
            }
            
            // 保存令牌和过期时间
            $this->accessToken = $data['access_token'];
            $this->tokenExpires = time() + $data['expires_in'];
            
        } catch (GuzzleException $e) {
            throw new \Exception('Failed to get PayPal access token: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录HTTP响应
     */
    private function logHttpResponse(string $endpoint, int $statusCode, string $responseBody, bool $isError = false): void
    {
        $this->logApiResponse($endpoint, [
            'status_code' => $statusCode,
            'body' => $responseBody
        ], !$isError);
    }
    
    /**
     * 映射PayPal状态到系统状态
     */
    private function mapPaypalStatus(string $paypalStatus): int
    {
        return match($paypalStatus) {
            'CREATED' => OrderStatusEnum::CREATED,
            'SAVED' => OrderStatusEnum::CREATED,
            'APPROVED' => OrderStatusEnum::PROCESSING,
            'VOIDED' => OrderStatusEnum::CLOSED,
            'COMPLETED' => OrderStatusEnum::SUCCESS,
            'PAYER_ACTION_REQUIRED' => OrderStatusEnum::PROCESSING,
            default => OrderStatusEnum::PROCESSING
        };
    }
    
    /**
     * 映射PayPal退款状态到系统状态
     */
    private function mapPaypalRefundStatus(string $paypalStatus): int
    {
        return match($paypalStatus) {
            'COMPLETED' => RefundStatusEnum::SUCCESS->value,
            'PENDING' => RefundStatusEnum::PROCESSING->value,
            'FAILED', 'CANCELLED' => RefundStatusEnum::FAILED->value,
            default => RefundStatusEnum::PROCESSING->value
        };
    }

    /**
     * 记录API响应日志
     */
    protected function logApiResponse(string $apiName, array $responseData, bool $isSuccess = true): void
    {
        $level = $isSuccess ? 'info' : 'error';
        Log::log($level, "Payment API Response: {$apiName}", [
            'channel' => $this->getIfCode(),
            'api' => $apiName,
            'response' => $responseData,
            'success' => $isSuccess
        ]);
    }

    /**
     * 捕获PayPal支付
     */
    public function captureOrder(string $token, string $payerId): array
    {
        try {
            $this->logApiRequest('captureOrder', ['token' => $token, 'payer_id' => $payerId]);
            
            // 调用PayPal API捕获支付
            $response = $this->callPaypalApi("v2/checkout/orders/{$token}/capture", [], 'POST');
            
            if (!isset($response['id'])) {
                throw new \Exception('PayPal capture order failed: ' . json_encode($response));
            }
            
            // 解析支付状态
            $status = $this->mapPaypalStatus($response['status'] ?? '');
            
            // 获取捕获ID - 这对退款操作至关重要
            $captureId = null;
            if (isset($response['purchase_units']) && is_array($response['purchase_units'])) {
                foreach ($response['purchase_units'] as $unit) {
                    if (isset($unit['payments']['captures'][0]['id'])) {
                        $captureId = $unit['payments']['captures'][0]['id'];
                        break;
                    }
                }
            }
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'], // 订单ID
                'capture_id' => $captureId, // 捕获ID，用于退款
                'status' => $status,
                'pay_data' => $response,
            ];
            
        } catch (\Exception $e) {
            $this->logError('PayPal captureOrder error', [
                'error' => $e->getMessage(),
                'token' => $token,
                'payer_id' => $payerId
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
}
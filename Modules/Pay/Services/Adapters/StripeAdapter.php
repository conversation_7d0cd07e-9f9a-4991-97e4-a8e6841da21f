<?php
// Services/Adapters/StripeAdapter.php
namespace Modules\Pay\Services\Adapters;

use Modules\Pay\Enums\OrderStatusEnum;
use Modules\Pay\Enums\PayErrorCode;
use Stripe\Webhook;
use Stripe\StripeClient;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Exception\ApiErrorException;

class StripeAdapter extends AbstractAdapter
{


    // https://dashboard.stripe.com/test/dashboard 沙盒地址

    /**
     * Stripe API操作类型
     */
    private const API_ACTION = [
        'CREATE_SESSION' => 'create_session',      // 创建支付会话
        'QUERY_SESSION' => 'query_session',        // 查询支付会话
        'QUERY_PAYMENT' => 'query_payment',        // 查询支付意向
        'CREATE_REFUND' => 'create_refund',        // 创建退款
        'QUERY_REFUND' => 'query_refund',          // 查询退款
    ];

    /**
     * 调用Stripe API
     * 
     * @param array $params 请求参数
     * @param string $method HTTP方法
     * @param string $action 操作类型
     * @return array
     */
    private function callStripeApi(array $params = [], string $method = 'POST', string $action = ''): array
    {
        $apiKey = $this->configParams['secret_key'] ?? '';
        if (empty($apiKey)) {
            throw new \Exception('Stripe API key is empty', PayErrorCode::PAY_CONFIG_NOT_FOUND->value);
        }
        
        try {
            $stripe = new StripeClient($apiKey);
            
            // 根据操作类型处理API调用
            $result = match($action) {
                self::API_ACTION['CREATE_SESSION'] => $stripe->checkout->sessions->create($params),
                self::API_ACTION['QUERY_SESSION'] => $stripe->checkout->sessions->retrieve($params['id'] ?? ''),
                self::API_ACTION['QUERY_PAYMENT'] => $stripe->paymentIntents->all($params),
                self::API_ACTION['CREATE_REFUND'] => $stripe->refunds->create($params),
                self::API_ACTION['QUERY_REFUND'] => $stripe->refunds->retrieve($params['id'] ?? ''),
                default => throw new \Exception("Unsupported action: {$action}")
            };
            // 转换为数组
            return json_decode(json_encode($result), true);
            
        } catch (ApiErrorException $e) {
            $this->logError('Stripe API error', [
                'action' => $action,
                'params' => $params,
                'error' => $e->getMessage(),
                'code' => $e->getHttpStatus()
            ]);
            throw new \Exception('Stripe API error: ' . $e->getMessage(), $e->getHttpStatus());
        } catch (\Exception $e) {
            $this->logError('Stripe SDK error', [
                'action' => $action,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Stripe SDK error: ' . $e->getMessage());
        }
    }

    /**
     * 统一下单
     */
    public function unifiedOrder(array $orderData): array
    {
        try {
            $this->logApiRequest('unifiedOrder', $orderData);
            // 1. 构建Stripe请求参数
            $requestParams = $this->buildStripeCheckoutParams($orderData);
            // 2. 调用Stripe API创建支付会话
            $response = $this->callStripeApi($requestParams, 'POST', self::API_ACTION['CREATE_SESSION']);
            
            if (!isset($response['id']) || !isset($response['client_secret'])) {
                throw new \Exception('Stripe create checkout session failed: ' . json_encode($response));
            }
            
            // 3. 记录API响应并返回客户端需要的数据
            $this->logApiResponse('unifiedOrder', $response, true);
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'],
                'client_secret' => $response['client_secret'],
                'pub_key' => $this->configParams['publishable_key'] ?? '',
                'pay_data' => $response,
                'pay_mode' => 'embedded', // 标识为嵌入式支付
            ];
            
        } catch (\Exception $e) {
            $this->logError('Stripe payment error', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 查询订单
     */
    public function queryOrder(string $orderNo): array
    {
        try {
            $this->logApiRequest('queryOrder', ['order_no' => $orderNo]);
            
            // 1. 检查是否是Stripe的会话ID
            if (substr($orderNo, 0, 3) === 'cs_') {
                // 查询Checkout Session
                $response = $this->callStripeApi(['id' => $orderNo], 'GET', self::API_ACTION['QUERY_SESSION']);
            } else {
                // 查询本地订单ID关联的支付意向
                $response = $this->callStripeApi([
                    'expand' => ['data.customer'],
                    'limit' => 1
                ], 'GET', self::API_ACTION['QUERY_PAYMENT']);
                
                // 手动过滤匹配的订单
                if (isset($response['data']) && is_array($response['data'])) {
                    foreach ($response['data'] as $intent) {
                        if (isset($intent['metadata']['order_id']) && $intent['metadata']['order_id'] === $orderNo) {
                            $response = $intent;
                            break;
                        }
                    }
                }
                
                if (!isset($response['id'])) {
                    throw new \Exception('Stripe payment intent not found');
                }
            }
            
            if (!isset($response['id'])) {
                throw new \Exception('Stripe query order failed: ' . json_encode($response));
            }
            
            // 2. 解析订单状态
            $status = $this->mapStripeStatus($response['status'] ?? '');
            
            $this->logApiResponse('queryOrder', $response, true);
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'],
                'status' => $status,
                'pay_data' => $response,
            ];
            
        } catch (\Exception $e) {
            $this->logError('Stripe query error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 关闭订单
     */
    public function closeOrder(string $orderNo): array
    {
        try {
            $this->logApiRequest('closeOrder', ['order_no' => $orderNo]);
            
            // 1. 查询支付意向
            $paymentIntent = $this->findPaymentIntentByOrderNo($orderNo);
            
            if (!$paymentIntent) {
                throw new \Exception('Stripe payment intent not found');
            }
            
            // 2. 取消支付意向
            $response = $this->callStripeApi([
                'id' => $paymentIntent['id'],
                'cancellation_reason' => 'requested_by_customer'
            ], 'POST', self::API_ACTION['QUERY_PAYMENT']);
            
            if (!isset($response['id'])) {
                throw new \Exception('Stripe cancel payment intent failed: ' . json_encode($response));
            }
            
            $this->logApiResponse('closeOrder', $response, true);
            
            return [
                'success' => true,
                'channel_order_no' => $response['id'],
                'status' => OrderStatusEnum::CLOSED,
                'pay_data' => $response,
            ];
            
        } catch (\Exception $e) {
            $this->logError('Stripe close order error', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 申请退款
     *
     * @param array $refundData 退款数据
     * @return array 退款结果
     */
    public function refund(array $refundData): array
    {
        try {
            $this->logApiRequest('refund', $refundData);
            
            // 获取并处理渠道订单号
            $channelOrderNo = $refundData['channel_order_no'] ?? '';
            
            if (empty($channelOrderNo)) {
                throw new \Exception('退款必须提供渠道订单号(channel_order_no)');
            }
            
            // 初始化Stripe客户端
            $apiKey = $this->configParams['secret_key'] ?? '';
            if (empty($apiKey)) {
                throw new \Exception('Stripe API key is empty', PayErrorCode::PAY_CONFIG_NOT_FOUND->value);
            }
            $stripe = new StripeClient($apiKey);
            
            // 根据ID前缀判断处理方式
            if (substr($channelOrderNo, 0, 3) === 'ch_') {
                // 1. 是Charge ID，可以直接使用
                $params = ['charge' => $channelOrderNo];
            } elseif (substr($channelOrderNo, 0, 3) === 'pi_') {
                // 2. 是Payment Intent ID
                $params = ['payment_intent' => $channelOrderNo];
            } elseif (substr($channelOrderNo, 0, 3) === 'cs_') {
                // 3. 是Checkout Session ID，需要获取关联的Payment Intent
                try {
                    $session = $stripe->checkout->sessions->retrieve($channelOrderNo);
                    
                    if (!empty($session->payment_intent)) {
                        // 3.1 Session已完成支付，有Payment Intent
                        $params = ['payment_intent' => $session->payment_intent];
                    } else {
                        // 3.2 Session尚未完成支付，无法退款
                        throw new \Exception('此订单尚未完成支付，无法退款');
                    }
                } catch (\Exception $e) {
                    throw new \Exception('无法从Checkout Session获取支付信息: ' . $e->getMessage());
                }
            } else {
                throw new \Exception('无效的渠道订单号格式: ' . $channelOrderNo);
            }
            
            // 添加其他退款参数
            if (!empty($refundData['refund_amount'])) {
                $params['amount'] = $refundData['refund_amount'];
            }
            
            if (!empty($refundData['refund_reason'])) {
                $params['reason'] = $this->mapRefundReason($refundData['refund_reason']);
            }
            
            // 添加元数据
            $params['metadata'] = [
                'refund_order_id' => $refundData['refund_order_id'] ?? '',
                'reason' => $refundData['refund_reason'] ?? '用户退款'
            ];
            
            // 直接调用Stripe API创建退款
            $refund = $stripe->refunds->create($params);
            // 使用对象序列化为数组
            $refundArray = json_decode(json_encode($refund), true);
            
            $this->logApiResponse('refund', $refundArray, true);
            
            return [
                'success' => true,
                'channel_refund_no' => $refund->id,
                'charge_id' => $refund->charge,
                'payment_intent' => $refund->payment_intent,
                'amount' => $refund->amount,
                'currency' => $refund->currency,
                'status' => $this->mapStripeRefundStatus($refund->status),
                'refund_data' => $refundArray,
            ];
        } catch (\Exception $e) {
            $this->logError('Stripe退款错误', [
                'error' => $e->getMessage(),
                'refund_data' => $refundData
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 退款查询
     * 
     * 查询Stripe退款状态
     * @see https://docs.stripe.com/refunds
     */
    public function queryRefund(string $refundNo): array
    {
        try {
            $this->logApiRequest('queryRefund', ['refund_no' => $refundNo]);
            
            // 查询退款
            $response = $this->callStripeApi([
                'id' => $refundNo
            ], 'GET', self::API_ACTION['QUERY_REFUND']);
            
            if (!isset($response['id'])) {
                throw new \Exception('Stripe查询退款失败: ' . json_encode($response));
            }
            
            // 解析退款状态
            $status = $this->mapStripeRefundStatus($response['status'] ?? '');
            
            // 检查是否失败并获取失败原因
            $failureReason = null;
            if ($response['status'] === 'failed' && isset($response['failure_reason'])) {
                $failureReason = $this->mapFailureReason($response['failure_reason']);
            }
            
            $this->logApiResponse('queryRefund', $response, true);
            
            $result = [
                'success' => true,
                'channel_refund_no' => $response['id'],
                'status' => $status,
                'refund_data' => $response,
            ];
            
            // 如果有失败原因，添加到结果中
            if ($failureReason) {
                $result['failure_reason'] = $failureReason;
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logError('Stripe查询退款错误', [
                'error' => $e->getMessage(),
                'refund_no' => $refundNo
            ]);
            
            return [
                'success' => false,
                'error_msg' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 映射退款原因到Stripe支持的原因
     * 
     * @param string $reason 退款原因
     * @return string Stripe支持的退款原因
     */
    private function mapRefundReason(string $reason): string
    {
        // Stripe支持的退款原因: duplicate, fraudulent, requested_by_customer, expired_uncaptured_charge
        $lowerReason = strtolower($reason);
        
        if (strpos($lowerReason, '重复') !== false || strpos($lowerReason, 'duplic') !== false) {
            return 'duplicate';
        }
        
        if (strpos($lowerReason, '欺诈') !== false || strpos($lowerReason, 'fraud') !== false) {
            return 'fraudulent';
        }
        
        if (strpos($lowerReason, '过期') !== false || strpos($lowerReason, 'expir') !== false) {
            return 'expired_uncaptured_charge';
        }
        
        // 默认为客户请求
        return 'requested_by_customer';
    }
    
    /**
     * 映射Stripe退款失败原因
     * 
     * @param string $failureReason Stripe的失败原因
     * @return string 本地化的失败原因描述
     */
    private function mapFailureReason(string $failureReason): string
    {
        return match($failureReason) {
            'charge_for_pending_refund_disputed' => '退款处理中客户发起了争议',
            'declined' => '退款被金融机构拒绝',
            'expired_or_canceled_card' => '支付方式已被取消或过期',
            'insufficient_funds' => '账户资金不足',
            'lost_or_stolen_card' => '卡片丢失或被盗',
            'merchant_request' => '商户请求取消退款',
            'unknown' => '未知原因',
            default => '退款失败: ' . $failureReason
        };
    }
    
    /**
     * 映射Stripe退款状态到系统状态
     */
    private function mapStripeRefundStatus(string $stripeStatus): int
    {
        return match($stripeStatus) {
            'succeeded', 'success' => 2, // 成功 (RefundStatusEnum::SUCCESS)
            'pending' => 1,              // 处理中 (RefundStatusEnum::PROCESSING)
            'failed' => 3,               // 失败 (RefundStatusEnum::FAILED)
            'canceled' => 4,             // 取消 (RefundStatusEnum::CLOSED)
            default => 1                 // 默认处理中 (RefundStatusEnum::PROCESSING)
        };
    }
    
    /**
     * 处理回调
     */
    public function handleNotify(array $requestData): array
    {
        try {
            // 记录原始请求数据
            $this->logPaymentNotify($requestData['data']['object']['client_reference_id'] ?? 'unknown', $requestData);
            
            // 1. 验证签名
            if (!$this->verifyWebhookSignature($requestData)) {
                throw new \Exception('Stripe webhook signature verification failed');
            }
            
            // 2. 处理支付事件
            $eventType = $requestData['type'] ?? '';
            
            // 只处理checkout.session.completed事件（支付成功）
            if ($eventType !== 'checkout.session.completed') {
                // 不处理其他类型的事件
                return [
                    'verified' => true,
                    'success' => false,
                    'message' => "只处理checkout.session.completed事件，当前事件: {$eventType}"
                ];
            }
            
            // 获取事件数据
            $eventData = $requestData['data']['object'] ?? [];
            
            if (empty($eventData)) {
                throw new \Exception('Stripe webhook event data is empty');
            }
            
            // 3. 提取订单信息
            $merchantOrderNo = $eventData['client_reference_id'] ?? '';
            
            // 如果找不到订单号，记录错误并抛出异常
            if (empty($merchantOrderNo)) {
                $this->logError('Order ID not found in Stripe webhook data', [
                    'event_data' => $eventData
                ]);
                throw new \Exception('Order ID not found in Stripe webhook data');
            }
            
            // 获取渠道订单号和金额
            $channelOrderNo = $eventData['id'] ?? '';
            $amount = $eventData['amount_total'] ?? 0;
            
            $this->logApiResponse('handleNotify', [
                'merchantOrderNo' => $merchantOrderNo,
                'channelOrderNo' => $channelOrderNo,
                'amount' => $amount,
                'event_type' => $eventType
            ], true);
            
            return [
                'verified' => true,
                'success' => true,
                'pay_order_id' => $merchantOrderNo,
                'channel_order_no' => $channelOrderNo,
                'amount' => $amount,
                'status' => OrderStatusEnum::SUCCESS,
                'pay_time' => date('Y-m-d H:i:s'),
                'raw_data' => $eventData
            ];
            
        } catch (\Exception $e) {
            $this->logError('Stripe webhook processing error', [
                'error' => $e->getMessage(),
                'request_data' => $requestData
            ]);
            
            return [
                'verified' => false,
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 签名生成
     */
    protected function generateSign(array $params): string
    {
        // Stripe不需要签名参数，返回空字符串
        return '';
    }
    
    /**
     * 签名验证
     */
    protected function verifySign(array $params, string $sign): bool
    {
        // Stripe不需要验证签名，返回true
        return true;
    }
    
    /**
     * 验证Webhook签名
     */
    private function verifyWebhookSignature(array $requestData): bool
    {
        try {
            $webhookSecret = 'whsec_4fbf4de046e80b411f44991d53ea2162c02f8d93d2ccacc9882291520f3dbf9c';
            // $webhookSecret = $this->configParams['webhook_secret'] ?? '';
            // if (empty($webhookSecret)) {
            //     // 如果未配置webhook密钥，记录警告
            //     $this->logError('Stripe webhook secret not configured', [
            //         'event_type' => $requestData['type'] ?? 'unknown'
            //     ]);
            //     // 开发环境可以返回true，生产环境应该验证
            //     return app()->environment('production') ? false : true;
            // }
            // 获取请求头中的签名
            $sigHeader = request()->header('Stripe-Signature');
            if (empty($sigHeader)) {
                $this->logError('Stripe-Signature header缺失', []);
                return false;
            }
            
            // 获取原始请求体
            $payload = request()->getContent();
            
            // 使用Stripe SDK验证签名
            Webhook::constructEvent(
                $payload,
                $sigHeader,
                $webhookSecret
            );
            
            return true;
        } catch (SignatureVerificationException $e) {
            // 签名验证失败
            $this->logError('Stripe webhook签名验证失败', [
                'error' => $e->getMessage()
            ]);
            return false;
        } catch (\Exception $e) {
            // 其他错误
            $this->logError('Stripe webhook验证错误', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 构建Stripe Checkout参数
     */
    private function buildStripeCheckoutParams(array $orderData): array
    {
        // 优先级：1.配置参数 2.订单数据 3.环境变量APP_URL 4.系统配置 5.默认值
        $returnUrl = $this->configParams['return_url'] 
            ?? (env('APP_URL') ? rtrim(env('APP_URL'), '/') . '/payment' : null);
        
        // 基本参数
        $params = [
            'ui_mode' => 'embedded',
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => strtolower($orderData['currency'] ?? 'usd'),
                        'product_data' => [
                            'name' => $orderData['subject'] ?? '订单支付',
                            'description' => $orderData['body'] ?? '',
                        ],
                        'unit_amount' => $orderData['amount'],
                    ],
                    'quantity' => 1,
                ]
            ],
            'metadata' => [
                'order_id' => $orderData['pay_order_id'],  // 主要关联字段
                'mch_order_no' => $orderData['mch_order_no'] ?? '',  // 保留商户订单号作为备用
            ],
            'client_reference_id' => $orderData['pay_order_id'],  // Stripe原生支持的关联ID
            'return_url' => $returnUrl . '?session_id={CHECKOUT_SESSION_ID}',
            'mode' => 'payment',
        ];
        
        return $params;
    }
    
    /**
     * 查找订单对应的支付意向
     * 
     * 注意：此方法仅用于辅助查询，实际退款请直接传入channel_order_no
     */
    private function findPaymentIntentByOrderNo(string $orderNo): ?array
    {
        try {
            // 查询checkout会话，通过client_reference_id查找
            $checkoutSessions = $this->callStripeApi([
                'limit' => 5
            ], 'GET', self::API_ACTION['QUERY_SESSION']);
            
            if (isset($checkoutSessions['data']) && is_array($checkoutSessions['data'])) {
                foreach ($checkoutSessions['data'] as $session) {
                    if (isset($session['client_reference_id']) && $session['client_reference_id'] === $orderNo) {
                        // 找到匹配的会话，获取关联的支付意向ID
                        if (!empty($session['payment_intent'])) {
                            // 查询完整的支付意向
                            if (is_string($session['payment_intent'])) {
                                return $this->callStripeApi([
                                    'id' => $session['payment_intent']
                                ], 'GET', self::API_ACTION['QUERY_PAYMENT']);
                            }
                            return $session['payment_intent'];
                        }
                    }
                }
            }
            
            return null;
        } catch (\Exception $e) {
            $this->logError('查找支付意向失败', [
                'error' => $e->getMessage(),
                'order_no' => $orderNo
            ]);
            return null;
        }
    }
    
    /**
     * 映射Stripe支付状态到系统状态
     */
    private function mapStripeStatus(string $stripeStatus): int
    {
        return match($stripeStatus) {
            'requires_payment_method', 'requires_confirmation', 'requires_action' => OrderStatusEnum::CREATED,
            'processing' => OrderStatusEnum::PROCESSING,
            'succeeded', 'complete' => OrderStatusEnum::SUCCESS,
            'canceled' => OrderStatusEnum::CLOSED,
            default => OrderStatusEnum::PROCESSING
        };
    }
} 
<?php

namespace Modules\Pay\Admin\Controllers;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Illuminate\Http\Request;
use Modules\Pay\Enums\PayChannelEnum;
use Modules\Pay\Enums\PayWayEnum;
use Modules\Pay\Services\PayConfigV2Service;

class PayConfigV2Controller
{
    protected PayConfigV2Service $payConfigService;

    public function __construct(PayConfigV2Service $payConfigService)
    {
        $this->payConfigService = $payConfigService;
    }

    /**
     * 获取支付配置列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $lang = $request->input('locale', app()->getLocale());
        
        return $this->payConfigService->configList($lang);
    }

    /**
     * 获取支付配置详情
     *
     * @param string $id
     * @return array
     * @throws BizException
     */
    public function show(string $id): array
    {
        $lang = request()->input('locale', app()->getLocale());
        
        // 将字符串ID转换为整数
        $configId = (int) $id;
        
        $config = $this->payConfigService->getConfigDetail($configId, $lang);
        
        if (!$config) {
            throw new BizException(Code::NOT_FOUND, '支付配置不存在');
        }
        
        return $config;
    }

    /**
     * 创建支付配置
     *
     * @param Request $request
     * @return array
     * @throws BizException
     */
    public function store(Request $request): array
    {
        $data = $request->validate([
            'if_code' => 'required|string',
            'if_name' => 'required|string',
            'way_code' => 'required|string',
            'if_rate' => 'required|numeric',
            'state' => 'required|integer|in:0,1',
            'lang' => 'nullable|string',
            'if_params' => 'nullable',
            'way_codes' => 'nullable',
            'bg_color' => 'nullable|string',
            'icon' => 'nullable|string',
            'remark' => 'nullable|string'
        ]);
        
        // 将state字段映射为status字段
        $data['status'] = $data['state'];
        unset($data['state']);
        
        // 处理way_codes，确保它是JSON字符串
        if (isset($data['way_codes']) && is_array($data['way_codes'])) {
            $data['way_codes'] = json_encode($data['way_codes']);
        }
        
        // 处理if_params，确保它是JSON字符串
        if (isset($data['if_params']) && is_array($data['if_params'])) {
            $data['if_params'] = json_encode($data['if_params']);
        } elseif (isset($data['if_params']) && is_string($data['if_params']) && !$this->isJson($data['if_params'])) {
            // 如果是字符串但不是JSON格式，尝试解析
            $data['if_params'] = json_encode($data['if_params']);
        }
        
        return $this->payConfigService->saveConfig($data, 0);
    }

    /**
     * 检查字符串是否为有效的JSON
     */
    private function isJson(string $string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * 更新支付配置
     *
     * @param Request $request
     * @param int $id
     * @return array
     * @throws BizException
     */
    public function update(Request $request, int $id): array
    {
        $data = $request->validate([
           'if_code' => 'required|string',
            'if_name' => 'required|string',
            'way_code' => 'required|string',
            'if_rate' => 'required|numeric',
            'state' => 'required|integer|in:0,1',
            'lang' => 'nullable|string',
            'if_params' => 'nullable',
            'way_codes' => 'nullable',
            'bg_color' => 'nullable|string',
            'icon' => 'nullable|string',
            'remark' => 'nullable|string'
        ]);
        
        // 处理语言
        if (isset($data['locale'])) {
            $data['lang'] = $data['locale'];
            unset($data['locale']);
        }
        
        // 处理way_codes，确保它是JSON字符串
        if (isset($data['way_codes']) && is_array($data['way_codes'])) {
            $data['way_codes'] = json_encode($data['way_codes']);
        }
        
        // 处理if_params，确保它是JSON字符串
        if (isset($data['if_params']) && is_array($data['if_params'])) {
            $data['if_params'] = json_encode($data['if_params']);
        }
        
        return $this->payConfigService->saveConfig($data, $id);
    }

    /**
     * 删除支付配置
     *
     * @param int $id
     * @return bool
     * @throws BizException
     */
    public function destroy(int $id): bool
    {
        return $this->payConfigService->deleteConfig($id);
    }

    /**
     * 更新支付配置状态
     *
     * @param Request $request
     * @return bool
     * @throws BizException
     */
    public function updateStatus(Request $request): bool
    {
        $data = $request->validate([
            'id' => 'required|integer',
            'status' => 'required|integer|in:0,1'
        ]);
        
        return $this->payConfigService->setStatus($data['id'], $data['status']);
    }

    /**
     * 获取可用的支付接口代码列表
     *
     * @return array
     */
    public function getIfCodes(): array
    {
        return $this->payConfigService->getIfCodes();
    }

    /**
     * 获取支付方式
     * 
     * @param Request $request
     * @return array
     */
    public function getPayWays(Request $request): array
    {
        $channel = $request->input('channel');
        
        if ($channel) {
            // 返回指定支付渠道支持的支付方式
            return [
                'data' => PayWayEnum::getByChannel($channel),
                'channel' => $channel,
                'channel_name' => PayChannelEnum::all()[$channel] ?? $channel,
            ];
        }
        
        // 返回所有支付方式和所有支付渠道
        return [
            'ways' => PayWayEnum::all(),
            'channels' => PayChannelEnum::all(),
            // 按渠道分组的支付方式
            'ways_by_channel' => [
                PayChannelEnum::ALIPAY => PayWayEnum::getByChannel(PayChannelEnum::ALIPAY),
                PayChannelEnum::WECHAT => PayWayEnum::getByChannel(PayChannelEnum::WECHAT),
                PayChannelEnum::PAYPAL => PayWayEnum::getByChannel(PayChannelEnum::PAYPAL),
                PayChannelEnum::STRIPE => PayWayEnum::getByChannel(PayChannelEnum::STRIPE),
                PayChannelEnum::PAYME => PayWayEnum::getByChannel(PayChannelEnum::PAYME),
                PayChannelEnum::FPS => PayWayEnum::getByChannel(PayChannelEnum::FPS),
                PayChannelEnum::RECEIPT => PayWayEnum::getByChannel(PayChannelEnum::RECEIPT),
            ]
        ];
    }
}

<!-- Modules/Pay/views/ui/configs/ConfigList.vue -->
<template>
    <div class="config-list-container">
      <div class="header">
        <el-button type="primary" @click="goToCreate">
          <el-icon><Plus /></el-icon>{{ $t('Pay.PayConfig.button.add') }}
        </el-button>
      </div>
      
      <div v-loading="configStore.loading">
        <el-row :gutter="20" class="config-row">
          <el-col :xs="24" :sm="12" :md="8" v-for="config in configStore.configs" :key="config.id" class="config-column">
            <el-card class="config-card">
              <div class="config-card-header">
                <div class="config-info">
                  <img :src="config.icon || getDefaultIcon(config.if_code)" class="config-icon" alt="支付图标">
                  <span class="config-title">{{ config.if_name }}</span>
                </div>
              </div>
              <div class="config-content">
                <div class="config-item">
                  <span class="label">{{ $t('Pay.PayConfig.label.if_code') }}</span>
                  <span>{{ config.if_code }}</span>
                </div>
                <div class="config-item">
                  <span class="label">{{ $t('Pay.PayConfig.label.way_code') }}</span>
                  <span>{{ config.way_code }}</span>
                </div>
                <div class="config-item">
                  <span class="label">{{ $t('Pay.PayConfig.label.rate') }}</span>
                  <span>{{ formatRate(config.if_rate) }}</span>
                </div>
                <div class="config-item">
                  <span class="label">{{ $t('Pay.PayConfig.label.status') }}</span>
                  <el-switch
                    :model-value="config.state === 1"
                    @update:model-value="(val) => handleStatusChange(config.id, val ? 1 : 0)"
                  />
                </div>
                <div class="config-actions">
                  <el-button type="primary" link @click="goToEdit(config.id)">
                    {{ $t('Pay.PayConfig.button.edit') }}
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <div v-if="configStore.configs.length === 0 && !configStore.loading" class="empty-data">
          {{ $t('Pay.PayConfig.empty') }}
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';
  import { useConfigStore } from '../../stores/configStore';
  import { useI18n } from 'vue-i18n'
  
  const router = useRouter();
  const configStore = useConfigStore();
  const { t } = useI18n()
  onMounted(() => {
    loadData();
  });
  
  // 加载数据
  const loadData = async () => {
    try {
      await configStore.fetchConfigs();
    } catch (error) {
      console.error('加载数据出错:', error);
      ElMessage.error(t('Pay.PayConfig.message.fetch_fail'));
    }
  };
  
  // 格式化费率显示（小数转百分比）
  const formatRate = (rate: number): string => {
    if (rate === undefined || rate === null) return '0%';
    return (rate * 100).toFixed(4) + '%';
  };
  
  // 切换状态
  const handleStatusChange = async (id: number, status: number) => {
    try {
      const result = await configStore.updateConfigStatus(id, status);
      if (result && (result.code === 0 || result.code === 200)) {
        ElMessage.success('状态更新成功');
        await loadData();
      } else {
        ElMessage.error((result && result.msg) || '状态更新失败');
        await loadData();
      }
    } catch (error) {
      console.error('状态更新错误:', error);
      ElMessage.error('状态更新失败');
      await loadData();
    }
  };
  
  // 获取默认图标
  const getDefaultIcon = (ifCode: string) => {
    const icons: Record<string, string> = {
      'stripe': '/admin/images/payment/stripe.png',
      'paypal': '/admin/images/payment/paypal.png',
      'alipay': '/admin/images/payment/alipay.png',
      'wechat': '/admin/images/payment/wechat.png',
      'payme': '/admin/images/payment/payme.png',
      'fps': '/admin/images/payment/fps.png'
    };
    
    return icons[ifCode] || '/admin/images/payment/default.png';
  };
  
  // 跳转到创建页面
  const goToCreate = () => {
    router.push('/pay/config/create');
  };
  
  // 跳转到编辑页面
  const goToEdit = (id: number) => {
    router.push(`/pay/config/${id}`);
  };
  </script>
  
  <style scoped>
  .config-list-container {
    padding: 20px;
    height:80%;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .config-card {
    margin-bottom: 0 !important;
    height: 100%;
    padding: 15px !important;
    transition: box-shadow 0.3s;
  }
  
  .config-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .config-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .config-info {
    display: flex;
    align-items: center;
  }
  
  .config-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
    object-fit: contain;
  }
  
  .config-title {
    font-weight: 500;
    font-size: 16px;
  }
  
  .config-content {
    margin-top: 10px;
  }
  
  .config-item {
    margin-bottom: 20px !important;
  }
  
  .label {
    width: 80px;
    color: #606266;
  }
  
  .config-actions {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
  }
  
  .empty-data {
    width: 100%;
    text-align: center;
    padding: 50px 0;
    color: #909399;
  }
  
  .config-column {
    margin-bottom: 20px !important;
    padding-bottom: 10px !important;
  }
  
  .config-row {
    margin-bottom: 10px;
    row-gap: 10px !important;
  }
  </style>
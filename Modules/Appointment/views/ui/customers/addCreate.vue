<template>
  <div class="table-page bwms-module">
    <div class="module-header">
    </div>
    
    <div class="module-con">
      <div class="scroll-bar-custom-transparent">
        <div class="box">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            v-loading="loading"
            class="customer-form"
          >
            <!-- 头像设置 -->
            <div class="form-section">
              <h3 class="section-title">{{ t('Appointment.CustomerCreate.avatar.title') }}</h3>
              <div class="avatar-section">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="handleAvatarChange"
                  accept="image/*"
                >
                  <div class="avatar-container">
                    <template v-if="imageUrl">
                      <img :src="imageUrl" class="avatar" />
                      <div class="avatar-hover-mask">
                        <el-icon><Edit /></el-icon>
                        <span>{{ t('Appointment.CustomerCreate.avatar.change') }}</span>
                      </div>
                    </template>
                    <template v-else>
                      <div class="upload-placeholder">
                        <el-icon><Plus /></el-icon>
                        <span>{{ t('Appointment.CustomerCreate.avatar.upload') }}</span>
                      </div>
                    </template>
                  </div>
                </el-upload>
              </div>
            </div>

            <!-- 客户信息 -->
            <div class="form-section">
              <h3 class="section-title">{{ t('Appointment.CustomerCreate.form.title') }}</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.CustomerCreate.form.name.label')"
                    prop="name"
                    required
                  >
                    <el-input 
                      v-model="form.name"
                      :placeholder="t('Appointment.CustomerCreate.form.name.placeholder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.CustomerCreate.form.email.label')"
                    prop="email"
                    required
                  >
                    <el-input 
                      v-model="form.email"
                      :placeholder="t('Appointment.CustomerCreate.form.email.placeholder')"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.CustomerCreate.form.phone.label')"
                    prop="phone"
                  >
                    <el-input 
                      v-model="form.phone"
                      :placeholder="t('Appointment.CustomerCreate.form.phone.placeholder')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.CustomerCreate.form.gender.label')"
                    prop="gender"
                  >
                    <el-select
                      v-model="form.gender"
                      :placeholder="t('Appointment.CustomerCreate.form.gender.placeholder')"
                      style="width: 100%"
                      clearable
                    >
                      <el-option 
                        :label="t('Appointment.CustomerCreate.form.gender.options.male')" 
                        value="1" 
                      />
                      <el-option 
                        :label="t('Appointment.CustomerCreate.form.gender.options.female')" 
                        value="2" 
                      />
                      <el-option 
                        :label="t('Appointment.CustomerCreate.form.gender.options.unknown')" 
                        value="0" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item 
                    :label="t('Appointment.CustomerCreate.form.birthdate.label')"
                    prop="birthdate"
                  >
                    <el-date-picker
                      v-model="form.birthdate"
                      type="date"
                      :placeholder="t('Appointment.CustomerCreate.form.birthdate.placeholder')"
                      style="width: 100%"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item 
                :label="t('Appointment.CustomerCreate.form.description.label')"
                prop="description"
              >
                <el-input
                  v-model="form.description"
                  type="textarea"
                  :placeholder="t('Appointment.CustomerCreate.form.description.placeholder')"
                  :rows="4"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
        <!-- 按钮组 -->
        <div class="form-buttons">
          <el-button class="button-cancel" @click="handleCancel">{{ t('Appointment.CustomerCreate.buttons.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? t('Appointment.CustomerCreate.buttons.saveChanges') : t('Appointment.CustomerCreate.buttons.saveAndAdd') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, UploadFile } from 'element-plus'
import { appointmentService } from '../../services/appointmentService'
import { Plus, Edit } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

// 路由
const router = useRouter()
const route = useRoute()

// 判断是否为编辑模式
const isEdit = ref(false)
const customerId = ref<number | null>(null)

// 表单引用
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// 头像预览
const imageUrl = ref('')

// 表单数据
const form = reactive({
  name: '',
  email: '',
  phone: null,
  gender: '' as string | number | null,
  birthdate: null,
  description: '',
  photo: ''
})

// 表单验证规则
const rules = computed(() => ({
  name: [
    { required: true, message: t('Appointment.CustomerCreate.form.name.required'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Appointment.CustomerCreate.form.name.length'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('Appointment.CustomerCreate.form.email.required'), trigger: 'blur' },
    { type: 'email', message: t('Appointment.CustomerCreate.form.email.format'), trigger: 'blur' }
  ],
  phone: [
    { pattern: /^\d{8,}$/, message: t('Appointment.CustomerCreate.form.phone.format'), trigger: 'blur' }
  ]
}))

// 更新类型定义
interface UploadFileInfo {
  raw: File
  name: string
  size?: number
  type?: string
}

// 处理头像变更
const handleAvatarChange = async (uploadFile: UploadFileInfo) => {
  const file = uploadFile.raw
  const isValidFormat = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidFormat) {
    ElMessage.error(t('Appointment.CustomerCreate.avatar.uploadError.formatError'))
    return false
  }
  if (!isLt10M) {
    ElMessage.error(t('Appointment.CustomerCreate.avatar.uploadError.sizeError'))
    return false
  }
  const formData = new FormData()
  formData.append('file', file)
  formData.append('dir', '/images/avatar')
  formData.append('mode', 'OVERWRITE')
  const { data } = await appointmentService.uploadCustomerAvatar(formData)
  if (data.code === 200 && data.data.file && data.data.file.url) {
    const newAvatarUrl = data.data.file.url
    imageUrl.value = newAvatarUrl
    form.photo = newAvatarUrl
    ElMessage.success(t('Appointment.CustomerCreate.avatar.uploadError.uploadSuccess'))
  } else {
    ElMessage.error(t('Appointment.CustomerCreate.avatar.uploadError.uploadFailed'))
  }
}

// 取消
const handleCancel = () => {
  router.push('/appointment/customers')
}

// 获取客户详情
const fetchCustomerDetail = async (id: number) => {
  loading.value = true
  try {
    const { data } = await appointmentService.getCustomerDetail(id)
    if (data.code === 200) {
      const customerData = data.data
      
      // 填充表单数据
      form.name = customerData.name
      form.email = customerData.email
      form.phone = customerData.phone
      form.gender = String(customerData.gender)
      form.birthdate = customerData.birthdate
      form.description = customerData.description || ''
      
      // 如果有头像，设置预览
      if (customerData.photo) {
        imageUrl.value = customerData.photo
        form.photo = customerData.photo
      }
    }
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerCreate.messages.getFailed'))
  } finally {
    loading.value = false
  }
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error(t('Appointment.CustomerCreate.messages.formError'))
      return
    }

    submitLoading.value = true
    try {
      // 构建请求数据
      const customerData = {
        name: form.name,
        email: form.email,
        phone: form.phone,
        gender: Number(form.gender),
        birthdate: form.birthdate,
        description: form.description,
        photo: form.photo
      }
      
      if (isEdit.value && customerId.value) {
        // 更新客户信息
        await appointmentService.updateCustomer(customerId.value, customerData)
        ElMessage.success(t('Appointment.CustomerCreate.messages.updateSuccess'))
      } else {
        // 创建新客户
        await appointmentService.createCustomer(customerData)
        ElMessage.success(t('Appointment.CustomerCreate.messages.createSuccess'))
      }
      
      router.push('/appointment/customers')
    } catch (error) {
      ElMessage.error(isEdit.value 
        ? t('Appointment.CustomerCreate.messages.updateFailed')
        : t('Appointment.CustomerCreate.messages.createFailed')
      )
    } finally {
      submitLoading.value = false
    }
  })
}

// 初始化
onMounted(() => {
  // 判断是否有ID参数，如果有则是编辑模式
  const id = route.params.id
  if (id && !Array.isArray(id)) {
    isEdit.value = true
    customerId.value = parseInt(id)
    fetchCustomerDetail(customerId.value)
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    padding: 16px;
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0,0,0,0.05);

      .customer-form {
        .form-section {
          margin-bottom: 32px;
          
          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #000;
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ebeef5;
          }
          
          .avatar-section {
            display: flex;
            justify-content: flex-start;
            
            .avatar-uploader {
              .avatar-container {
                width: 140px;
                height: 140px;
                border: 1px dashed #dcdfe6;
                border-radius: 4px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                transition: all 0.3s;
                position: relative;
                overflow: hidden;
                
                &:hover {
                  border-color: #409eff;
                  
                  .avatar-hover-mask {
                    opacity: 1;
                  }
                }
                
                .avatar {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  display: block;
                }

                .avatar-hover-mask {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background: rgba(0, 0, 0, 0.6);
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  opacity: 0;
                  transition: opacity 0.3s;
                  color: #fff;

                  .el-icon {
                    font-size: 20px;
                    margin-bottom: 8px;
                  }

                  span {
                    font-size: 14px;
                  }
                }

                .upload-placeholder {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  color: #8c939d;

                  .el-icon {
                    font-size: 28px;
                    margin-bottom: 8px;
                  }

                  span {
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 26px;
  padding: 0 20px;
}


:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input.is-focus .el-input__wrapper),
:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  
  &:focus {
    box-shadow: 0 0 0 1px #409eff inset;
  }
}
</style>

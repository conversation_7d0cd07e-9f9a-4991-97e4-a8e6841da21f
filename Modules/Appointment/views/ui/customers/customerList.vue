<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <h1></h1>
      <div class="btn-list">
        <el-button @click="handleImport">
          <el-icon><img src="/resources/admin/assets/icon/UploadIcon.png" alt="UploadIcon" /></el-icon>
          <span>{{ t('Appointment.CustomerList.import_text') }}</span>
        </el-button>
        <el-button @click="handleExport">
          <el-icon><img src="/resources/admin/assets/icon/DownloadIcon.png" alt="DownloadIcon" /></el-icon>
          <span>{{ t('Appointment.CustomerList.export_text') }}</span>
        </el-button>
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="t('Cms.list.filter')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="t('Appointment.CustomerList.keywordPlaceholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreate">
          <el-icon><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
          <span>{{ t('Appointment.CustomerList.create') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
        <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
        <el-table-column type="selection" width="55" />
          <el-table-column :label="t('Appointment.CustomerList.customer')" min-width="200">
            <template #default="{ row }">
              <div class="customer-info">
                <img class="info-avatar" :src="row?.photo || avatarUrl" @error="handleAvatarError" />
                <div class="customer-detail">
                  <div class="customer-name">{{ row.name }}</div>
                  <div class="customer-email">{{ row.email }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="phone" :label="t('Appointment.CustomerList.phone')" width="150" />
          <el-table-column prop="latest_appointment_time" :label="t('Appointment.CustomerList.latestAppointment')" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.latest_appointment_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="appointment_count" :label="t('Appointment.CustomerList.appointmentCount')" width="100" />
          <el-table-column prop="created_at" :label="t('Appointment.CustomerList.registrationDate')" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('Appointment.CustomerList.operations')" width="160" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn" @click="handleView(row)">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/ViewIcon.png" alt="ViewIcon" /></el-icon>
                </div>
                <div class="bwms-operate-btn" @click="handleEdit(row)">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" /></el-icon>
                </div>
                <div class="bwms-operate-btn" @click="handleDelete(row)">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table> 
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
              />
            </el-select>
            <span class="total-text">{{ t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              background
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      :title="t('Appointment.CustomerList.import.title')"
      width="500px"
      class="el-dialog-common-cls"
    >
      <div class="import-container">
        <el-upload
          class="upload-demo"
          drag
          action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            {{ t('Appointment.CustomerList.import.uploadTip') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ t('Appointment.CustomerList.import.fileTypeTip') }}
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <el-button type="primary" link @click="downloadTemplate">
            {{ t('Appointment.CustomerList.import.downloadTemplate') }}
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">{{ t('Appointment.CustomerList.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedFile"
          >
            {{ t('Appointment.CustomerList.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View, Edit, Plus, Search, Upload, Download, UploadFilled, Filter, Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { getBaseUrl } from '/admin/support/helper'

// 初始化i18n
const { t } = useI18n()

// 获取基础URL
const baseURL = getBaseUrl()

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 定义客户类型接口
interface Customer {
  id: number
  name: string
  email: string
  phone: string
  avatar?: string
  last_appointment?: string
  appointment_count: number
  created_at: string
}

// 表格数据类型定义
const tableData = ref<Customer[]>([])
const loading = ref(false)
const selectedRows = ref<Customer[]>([])
const avatarUrl = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 导入对话框
const importDialogVisible = ref(false)

// 路由
const router = useRouter()

// 添加头像加载错误处理函数
const handleAvatarError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  imgElement.src = avatarUrl.value
}

// 格式化日期时间
const formatDateTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}

// 添加客户
const handleCreate = () => {
  router.push({
    path: '/appointment/customers/create'
  })
}

// 查看客户
const handleView = (row: Customer) => {
  router.push({
    path: `/appointment/customers/${row.id}/detail`
  })
}

// 编辑客户
const handleEdit = (row: Customer) => {
  router.push({
    path: `/appointment/customers/${row.id}/edit`
  })
}

// 处理选择变化
const handleSelectionChange = (rows: Customer[]) => {
  selectedRows.value = rows
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  showFilterDropdown.value = false
  handleSearch()
}

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRows.value.length) return
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 个客户吗？`,
    '删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const ids = selectedRows.value.map(row => row.id)
      await appointmentService.deleteCustomer(ids)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}
// 单个删除
const handleDelete = (row: Customer) => {
  ElMessageBox.confirm(
    t('Appointment.CustomerList.confirmDelete'),
    t('Appointment.CustomerList.tip'),
    {
      confirmButtonText: t('Appointment.CustomerList.confirm'),
      cancelButtonText: t('Appointment.CustomerList.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await appointmentService.deleteCustomer([row.id])
      ElMessage.success(t('Appointment.CustomerList.deleteSuccess'))
      fetchData()
    } catch (error) {
      ElMessage.error(t('Appointment.CustomerList.deleteFailed'))
    }
  })
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出
const handleExport = async () => {
  try {
    loading.value = true
    const exportUrl = `${baseURL}reservation/customers/export?page=${pagination.page}&limit=${pagination.pageSize}`
    const link = document.createElement('a')
    link.href = exportUrl
    link.setAttribute('download', `客户列表_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success(t('Appointment.CustomerList.export.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerList.export.exportFailed'))
  } finally {
    loading.value = false
  }
}

// 添加导入相关变量
const selectedFile = ref<any>()
const uploading = ref(false)

// 文件选择改变
const handleFileChange = (uploadFile: any) => {
  selectedFile.value = uploadFile
}

// 处理上传按钮点击
const handleUpload = async () => {
  if (!selectedFile.value?.raw) {
    ElMessage.warning(t('Appointment.CustomerList.import.selectFile'))
    return
  }

  uploading.value = true
  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value.raw)
    
    await appointmentService.importCustomers(formData)
    ElMessage.success(t('Appointment.CustomerList.import.importSuccess'))
    importDialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerList.import.importFailed'))
  } finally {
    uploading.value = false
  }
}

// 下载模板
const downloadTemplate = () => {
  try {
    const link = document.createElement('a')
    link.href = `${baseURL}reservation/customers/export-template`
    link.download = '客户导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerList.import.downloadFailed'))
  }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await appointmentService.getCustomerList({
      page: pagination.page,
      limit: pagination.pageSize,
      keyword: searchForm.keyword
    })
    
    if (data.code === 200) {
      tableData.value = data.data.items
      pagination.total = data.data.total
    }
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerList.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      color: #303133;
    }
    
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;

      .customer-info {
        display: flex;
        align-items: center;
        gap: 12px;
        .info-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          background-color: #f5f7fa;
        }
        .customer-detail {
          .customer-name {
            font-size: 14px;
            color: #303133;
          }
          
          .customer-email {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  flex-shrink: 0; // 防止分页区域被压缩
}

.import-container {
  .template-download {
    margin-top: 16px;
  }
}
</style>

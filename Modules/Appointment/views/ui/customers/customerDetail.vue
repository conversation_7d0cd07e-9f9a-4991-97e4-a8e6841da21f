<template>
  <div class="table-page bwms-module">
    <div class="module-con">
      <div class="box">
        <!-- 标签页导航 -->
        <el-tabs v-model="activeTab" class="customer-tabs" @tab-change="handleTabChange">
          <el-tab-pane :name="'info'" :label="t('Appointment.CustomerDetail.tabs.info')">
            <div class="customer-info-container" v-loading="loading">
              <!-- 头像和基本信息 -->
              <div class="customer-profile">
                <div class="profile-left">
                  <el-avatar 
                    :size="100" 
                    :src="form.photo || '/images/default-avatar.png'"
                    class="customer-avatar"
                  />
                </div>
                <div class="profile-right">
                  <div class="customer-name">{{ form.name }}</div>
                </div>
              </div>

              <!-- 详细信息网格 -->
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">{{ t('Appointment.CustomerDetail.profile.email') }}</div>
                  <div class="info-value">{{ form.email }}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">{{ t('Appointment.CustomerDetail.profile.phone') }}</div>
                  <div class="info-value">{{ form.phone || '--' }}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">{{ t('Appointment.CustomerDetail.profile.birthdate') }}</div>
                  <div class="info-value">{{ form.birthdate || '--' }}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">{{ t('Appointment.CustomerDetail.profile.appointmentCount') }}</div>
                  <div class="info-value">{{ form.appointment_count || 0 }}</div>
                </div>
                
                <div class="info-item">
                  <div class="info-label">{{ t('Appointment.CustomerDetail.profile.gender') }}</div>
                  <div class="info-value">
                    {{ form.gender === '1' ? t('Appointment.CustomerDetail.profile.genderOptions.male') : 
                       (form.gender === '2' ? t('Appointment.CustomerDetail.profile.genderOptions.female') : 
                       t('Appointment.CustomerDetail.profile.genderOptions.unknown')) }}
                  </div>
                </div>
                
                <div class="info-item" v-if="form.description">
                  <div class="info-label">{{ t('Appointment.CustomerDetail.profile.description') }}</div>
                  <div class="info-value">{{ form.description }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane :name="'history'" :label="t('Appointment.CustomerDetail.tabs.history')">
            <div class="appointment-history" v-loading="historyLoading">
              <el-timeline v-if="appointmentHistory.length > 0">
                <el-timeline-item
                  v-for="(appointment, index) in appointmentHistory"
                  :key="index"
                  :color="'#409EFF'"
                >
                  <div class="timeline-content">
                    <div class="timeline-header">
                      {{ formatDateTime(appointment.appointment_date) }}
                      <el-icon class="icon" :style="{ color: appointment.status === 1 ? '#67c23a' : '#f56c6c' }">
                        <CircleCheck v-if="appointment.status === 1" />
                        <CircleClose v-else />
                      </el-icon>
                    </div>
                    <div class="timeline-body">
                      <div class="info-row">
                        <span class="label">{{ t('Appointment.CustomerDetail.history.appointment.serviceType') }}</span>
                        <span class="value">{{ appointment.service_name }}</span>
                      </div>
                      <div class="info-row">
                        <span class="label">{{ t('Appointment.CustomerDetail.history.appointment.staff') }}</span>
                        <span class="value">{{ appointment.staff_names }}</span>
                      </div>
                      <div class="info-row">
                        <span class="label">{{ t('Appointment.CustomerDetail.history.appointment.location') }}</span>
                        <span class="value">{{ appointment.location }}</span>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
              
              <el-empty v-else :description="t('Appointment.CustomerDetail.history.empty')"></el-empty>
              
              <!-- 分页 -->
              <div v-if="appointmentHistory.length > 0" class="pagination-container">
                <div class="page-info">
                  {{ currentPage }}/{{ totalPages }}
                </div>
                <div class="page-buttons">
                  <el-button 
                    :disabled="currentPage <= 1"
                    @click="handlePrevPage"
                  >
                    {{ t('Appointment.CustomerDetail.history.pagination.prev') }}
                  </el-button>
                  <el-button 
                    :disabled="currentPage >= totalPages"
                    @click="handleNextPage"
                  >
                    {{ t('Appointment.CustomerDetail.history.pagination.next') }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'

// 初始化i18n
const { t } = useI18n()

// 定义预约历史记录类型
interface AppointmentHistoryItem {
  id: number
  appointment_date: string
  location: string
  service_name?: string
  service_id: number
  status: number
  staff_names?: string
  original_price: string
  final_price: string
  payment_status: number
  remark: string | null
}

// 路由
const router = useRouter()
const route = useRoute()

// 加载状态
const loading = ref(false)
const historyLoading = ref(false)

// 客户ID
const customerId = ref<number | null>(null)

// 当前选中的标签页
const activeTab = ref('info')

// 表单数据
const form = reactive({
  name: '',
  email: '',
  phone: '',
  birthdate: '',
  description: '',
  photo: '',
  appointment_count: 0,
  gender: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
const totalPages = ref(0)

// 预约历史数据
const appointmentHistory = ref<AppointmentHistoryItem[]>([])

// 获取客户详情
const fetchCustomerDetail = async () => {
  if (!customerId.value) return
  
  loading.value = true
  try {
    const { data } = await appointmentService.getCustomerDetail(customerId.value)
    if (data.code === 200) {
      const customerData = data.data
      
      // 填充表单数据
      form.name = customerData.name
      form.email = customerData.email
      form.phone = customerData.phone
      form.birthdate = customerData.birthdate
      form.description = customerData.description || ''
      form.photo = customerData.photo
      form.appointment_count = customerData.appointment_count || 0
      form.gender = customerData.gender
    }
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerDetail.error.getCustomerFailed'))
  } finally {
    loading.value = false
  }
}

// 获取客户预约历史
const fetchAppointmentHistory = async () => {
  if (!customerId.value) return
  
  historyLoading.value = true
  try {
    const { data } = await appointmentService.getCustomerAppointments(customerId.value, {
      page: currentPage.value,
      limit: pageSize.value,
    })
    
    if (data.code === 200) {
      // 转换预约历史数据
      const items = data.data.items || [];
      appointmentHistory.value = items.map((item: any) => {
        return {
          id: item.id,
          appointment_date: item.appointment_date,
          location: item.location,
          service_id: item.service_id,
          service_name: item.service_name,
          status: item.status,
          staff_names: item.staff_names || '--',
          original_price: item.original_price,
          final_price: item.final_price,
          payment_status: item.payment_status,
          remark: item.remark
        }
      })
      
      totalItems.value = data.data.total || 0
      totalPages.value = Math.ceil((data.data.total || 0) / pageSize.value) || 1
    }
  } catch (error) {
    ElMessage.error(t('Appointment.CustomerDetail.error.getHistoryFailed'))
  } finally {
    historyLoading.value = false
  }
}

// 处理翻页
const handlePrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchAppointmentHistory()
  }
}

const handleNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    fetchAppointmentHistory()
  }
}

// 监听标签页变化
const handleTabChange = (tab: string) => {
  if (tab === 'history') {
    currentPage.value = 1
    fetchAppointmentHistory()
  }
}

// 格式化日期时间
const formatDateTime = (datetime: string | null) => {
  if (!datetime) return '--'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm')
}


// 初始化
onMounted(() => {
  // 获取客户ID
  const id = route.params.id
  if (id && !Array.isArray(id)) {
    customerId.value = parseInt(id)
    fetchCustomerDetail()
  }
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  margin-top: 16px;
  
  .module-con {
    flex: 1;
    overflow: auto;
    
    .box {
      padding-top: 0;

      .customer-tabs {
        :deep(.el-tabs__header) {
          margin: 0;
          border-bottom: 1px solid #ebeef5;
        }

        :deep(.el-tabs__nav-wrap::after) {
          display: none;
        }

        :deep(.el-tabs__item) {
          height: 50px;
          line-height: 50px;
          font-size: 14px;
          color: #606266;
          
          &.is-active {
            color: #002140;
            font-weight: 500;
          }
        }
      }

      .customer-info-container {
        padding: 24px;

        .customer-profile {
          display: flex;
          align-items: center;
          gap: 24px;
          margin-bottom: 32px;
          padding-bottom: 32px;
          border-bottom: 1px solid #ebeef5;

          .profile-left {
            flex-shrink: 0;
            
            .customer-avatar {
              border-radius: 50%;
              border: 1px solid #ebeef5;
            }
          }

          .profile-right {
            .customer-name {
              font-size: 24px;
              font-weight: 500;
              color: #303133;
              line-height: 1.4;
            }
          }
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 24px;

          .info-item {
            .info-label {
              font-size: 14px;
              color: #909399;
              margin-bottom: 8px;
            }

            .info-value {
              font-size: 16px;
              color: #303133;
            }
          }
        }
      }

      .appointment-history {
        padding: 24px;
        
        .el-timeline {
          padding-left: 0;
          
          .el-timeline-item {
            padding-bottom: 25px;
            
            &:last-child {
              padding-bottom: 0;
            }
            
            .el-timeline-item__timestamp {
              color: #409EFF;
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 10px;
            }
            
            .el-timeline-item__content {
              margin-top: 5px;
            }
            
            .el-timeline-item__node {
              background-color: transparent;
            }
            .timeline-content {
              .timeline-header {
                display: flex;
                align-items: center;
                gap: 8px;
              }
              .timeline-body {
                background-color: #F8F9FB;
                border-radius: 8px;
                padding: 16px;
                margin-top: 8px;
                
                .info-row {
                  display: flex;
                  margin-bottom: 12px;
                  align-items: center;
                  
                  &:last-child {
                    margin-bottom: 0;
                  }
                  
                  .label {
                    width: 80px;
                    color: #909399;
                    font-size: 14px;
                  }
                  
                  .value {
                    color: #303133;
                    font-size: 14px;
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }
        
        .pagination-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 24px;
          padding-top: 16px;
          border-top: 1px solid #ebeef5;
          
          .page-info {
            color: #606266;
            font-size: 14px;
          }
          
          .page-buttons {
            display: flex;
            gap: 12px;
          }
        }
      }
    }
  }
}
</style> 
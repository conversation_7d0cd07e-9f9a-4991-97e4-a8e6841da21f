<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <h1></h1>
      <div class="btn-list">
        <el-button class="button-no-border" @click="handleImport">
          <el-icon><img src="/resources/admin/assets/icon/UploadIcon.png" alt="ImportIcon" /></el-icon>
          <span>{{ t('Appointment.StaffList.buttons.import') }}</span>
        </el-button>
        <el-button class="button-no-border" @click="handleExport">
          <el-icon><img src="/resources/admin/assets/icon/DownloadIcon.png" alt="ExportIcon" /></el-icon>
          <span>{{ t('Appointment.StaffList.buttons.export') }}</span>
        </el-button>
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="t('Cms.list.filter')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="t('Appointment.StaffList.search.placeholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          <span>{{ t('Appointment.StaffList.buttons.create') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
        <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
          <el-table-column prop="name" :label="t('Appointment.StaffList.table.name')" min-width="180" />
          <el-table-column prop="position_name" :label="t('Appointment.StaffList.table.position')" width="200">
            <template #default="{ row }">
              {{ row.position_name || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="department_name" :label="t('Appointment.StaffList.table.department')" width="180">
            <template #default="{ row }">
              {{ row.department_name || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="email" :label="t('Appointment.StaffList.table.email')" width="260">
            <template #default="{ row }">
              {{ row.email || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" :label="t('Appointment.StaffList.table.phone')" width="180">
            <template #default="{ row }">
              {{ row.phone || '--' }}
            </template>
          </el-table-column>
          <el-table-column :label="t('Appointment.StaffList.table.operations')" width="150" fixed="right">
            <template #default="{ row }">
              <el-button 
                link 
                type="primary" 
                @click="handleEdit(row)"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button 
                link 
                type="primary" 
                @click="handleDeleteSingle(row)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      :title="t('Appointment.StaffList.import.title')"
      width="500px"
      class="el-dialog-common-cls"
    >
      <div class="import-container">
        <el-upload
          class="upload-demo"
          drag
          action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            {{ t('Appointment.StaffList.import.uploadTip') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ t('Appointment.StaffList.import.fileTypeTip') }}
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <el-button type="primary" link @click="downloadTemplate">{{ t('Appointment.StaffList.import.downloadTemplate') }}</el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">{{ t('Appointment.Common.buttons.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedFile"
          >
            {{ t('Appointment.Common.buttons.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Plus, Search, More, Upload, Download, UploadFilled, Edit, Delete, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { appointmentService } from '../../services/appointmentService'
import { env, getBaseUrl } from '/admin/support/helper'

// 初始化国际化
const { t } = useI18n()

// 定义基础URL
const baseURL = getBaseUrl()

// 定义员工接口
interface Staff {
  id: number
  name: string
  email: string
  phone: string
  department_name: string
  position_name: string
  status: string
  photo: string
}

// 定义搜索表单接口
interface SearchForm {
  keyword: string
}

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 导入对话框
const importDialogVisible = ref(false)
const importLoading = ref(false)
const selectedFile = ref<any>()
const uploading = ref(false)

// 搜索表单
const searchForm = reactive<SearchForm>({
  keyword: ''
})

// 表格数据
const tableData = ref<Staff[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 4
})

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 查询
const handleSearch = () => {
  pagination.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  showFilterDropdown.value = false
  handleSearch()
}

// 创建员工
const handleCreate = () => {
  router.push('/appointment/staff/create')
}

// 编辑员工
const handleEdit = (row: Staff) => {
  router.push(`/appointment/staff/${row.id}/edit`)
}

// 删除单个员工
const handleDeleteSingle = (row: Staff) => {
  ElMessageBox.confirm(
    t('Appointment.StaffList.messages.deleteConfirm', { name: row.name }),
    t('Appointment.StaffList.messages.deleteTitle'),
    {
      confirmButtonText: t('Appointment.Common.buttons.confirm'),
      cancelButtonText: t('Appointment.Common.buttons.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      await appointmentService.deleteStaff([row.id])
      ElMessage.success(t('Appointment.StaffList.messages.deleteSuccess'))
      fetchData()
    } catch (error: any) {
      ElMessage.error(error.message || t('Appointment.StaffList.messages.deleteFailed'))
    }
  }).catch(() => {
    ElMessage.info(t('Appointment.StaffList.messages.deleteCancel'))
  })
}

// 文件选择改变
const handleFileChange = (uploadFile: any) => {
  selectedFile.value = uploadFile
}

// 下载导入模板
const downloadTemplate = () => {
  try {
    const link = document.createElement('a')
    link.href = `${baseURL}reservation/staff/export-template`
    link.download = t('Appointment.StaffList.import.templateFileName')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    ElMessage.error(t('Appointment.StaffList.import.downloadFailed'))
  }
}

// 处理上传按钮点击
const handleUpload = async () => {
  if (!selectedFile.value?.raw) {
    ElMessage.warning(t('Appointment.StaffList.import.selectFile'))
    return
  }

  uploading.value = true
  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value.raw)
    
    const { data } = await appointmentService.importStaff(formData)
    
    if (data.code === 200) {
      ElMessage.success(t('Appointment.StaffList.import.importSuccess'))
      importDialogVisible.value = false
      fetchData()
    } else {
      ElMessage.error(data.message || t('Appointment.StaffList.import.importFailed'))
    }
  } catch (error: any) {
    // ElMessage.error(error.message || '导入失败')
  } finally {
    uploading.value = false
  }
}

// 处理导出
const handleExport = async () => {
  try {
    loading.value = true
    const exportUrl = `${baseURL}reservation/staff/export?page=${pagination.page}&limit=${pagination.pageSize}`
    const link = document.createElement('a')
    link.href = exportUrl
    link.setAttribute('download', `${t('Appointment.StaffList.export.fileName')}_${new Date().toISOString().slice(0, 10)}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success(t('Appointment.StaffList.export.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('Appointment.StaffList.export.exportFailed'))
  } finally {
    loading.value = false
  }
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      keyword: searchForm.keyword || undefined
    }
    
    const response = await appointmentService.getStaffList(params)
    if (response.data && response.data.code === 200) {
      tableData.value = response.data.data.items
      pagination.total = response.data.data.total
    } else {
      ElMessage.error(response.data?.message || t('Appointment.StaffList.messages.getListFailed'))
    }
  } catch (error: any) {
    ElMessage.error(error.message || t('Appointment.StaffList.messages.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 处理导入
const handleImport = () => {
  importDialogVisible.value = true
  selectedFile.value = undefined
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      flex: 1;
      padding-top: 20px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      .el-table {
        flex: 1;
        overflow: auto;
      }
    }
  }
}


.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

// 导入容器样式
.import-container {
  padding: 10px;
  
  .template-download {
    margin-top: 15px;
  }
}
</style>

<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="scroll-bar-custom-transparent">
        <div class="box" v-loading="loading">
          <!-- 选项卡 -->
          <el-tabs v-model="activeTab" class="setting-tabs">
            <el-tab-pane :label="t('Appointment.Settings.tabs.basic')" name="basic" />
            <el-tab-pane :label="t('Appointment.Settings.tabs.holiday')" name="holiday" />
            <el-tab-pane :label="t('Appointment.Settings.tabs.notification')" name="notification" />
          </el-tabs>

          <!-- 基础设置内容 -->
          <div v-show="activeTab === 'basic'" class="tab-content">
            <el-form :model="basicSettings" label-width="auto">
              <!-- 预约限制 -->
              <div class="setting-section">
                <div class="setting-section-title">{{ t('Appointment.Settings.basic.bookingLimit.title') }}</div>
                <el-form-item>
                  <el-select v-model="basicSettings.bookingLimit" placeholder="请选择" style="width: 50%">
                    <el-option :label="t('Appointment.Settings.basic.bookingLimit.memberOnly')" value="member_only" />
                    <el-option :label="t('Appointment.Settings.basic.bookingLimit.all')" value="no_limit" />
                  </el-select>
                </el-form-item>
              </div>

              <!-- 非会员预约是否保存历史记录 -->
              <div class="setting-section">
                <div class="setting-section-title">{{ t('Appointment.Settings.basic.saveGuestHistory.title') }}</div>
                <el-form-item>
                  <el-radio-group v-model="basicSettings.saveGuestHistory">
                    <el-radio :label="true">{{ t('Appointment.Settings.basic.saveGuestHistory.yes') }}</el-radio>
                    <el-radio :label="false">{{ t('Appointment.Settings.basic.saveGuestHistory.no') }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>

              <!-- 时段跨度设置 -->
              <div class="setting-section">
                <div class="setting-section-title">{{ t('Appointment.Settings.basic.timeSlot.title') }}</div>
                <el-form-item>
                  <el-select v-model="basicSettings.timeSlotDuration" style="width: 50%" placeholder="请选择">
                    <el-option :label="t('Appointment.Settings.basic.timeSlot.5min')" value="5" />
                    <el-option :label="t('Appointment.Settings.basic.timeSlot.15min')" value="15" />
                    <el-option :label="t('Appointment.Settings.basic.timeSlot.30min')" value="30" />
                  </el-select>
                  <el-checkbox v-model="basicSettings.enableCustomTimeSlots" class="time-slot-checkbox">
                    {{ t('Appointment.Settings.basic.timeSlot.customTimeSlot') }}
                  </el-checkbox>
                </el-form-item>
              </div>

              <!-- 提前预定设置 -->
              <div class="setting-section">
                <div class="setting-section-title">
                  <span>{{ t('Appointment.Settings.basic.advanceBooking.title') }}</span>
                  <el-switch v-model="basicSettings.enableAdvanceBooking" class="ml-auto" />
                </div>
                
                <div v-if="basicSettings.enableAdvanceBooking" class="advance-booking-input-container">
                  <el-input-number min="1" v-model="basicSettings.advanceBookingValue" :placeholder="t('Appointment.Settings.basic.advanceBooking.value')" class="advance-booking-input" />
                  <el-select v-model="basicSettings.advanceBookingUnit" class="advance-unit-select" placeholder="请选择">
                    <el-option :label="t('Appointment.Settings.basic.advanceBooking.minute')" value="mixed" />
                    <el-option :label="t('Appointment.Settings.basic.advanceBooking.hour')" value="hour" />
                    <el-option :label="t('Appointment.Settings.basic.advanceBooking.day')" value="day" />
                  </el-select>
                </div>
              </div>
              <!-- 可以预定天数 -->
              <div class="setting-section">
                <div class="setting-section-title">{{ t('Appointment.Settings.basic.bookingDays.title') }}</div>
                <el-form-item>
                  <el-input v-model="basicSettings.advanceBookingDays" :placeholder="t('Appointment.Settings.basic.bookingDays.placeholder')" style="width: 50%;" />
                </el-form-item>
              </div>
              <!-- 默认预约状态 -->
              <div class="setting-section">
                <div class="setting-section-title">{{ t('Appointment.Settings.basic.defaultStatus.title') }}</div>
                <el-form-item>
                  <el-select v-model="basicSettings.defaultBookingStatus" style="width: 50%" placeholder="请选择">
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.pending')" value="0" />
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.confirmed')" value="1" />
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.rescheduled')" value="2" />
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.completed')" value="3" />
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.cancelled')" value="4" />
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.rejected')" value="5" />
                    <el-option :label="t('Appointment.Settings.basic.defaultStatus.noShow')" value="6" />
                  </el-select>
                </el-form-item>
              </div>

              <!-- 默认付款成功状态 -->
              <div class="setting-section">
                <div class="setting-section-title">{{ t('Appointment.Settings.basic.defaultPaymentStatus.title') }}</div>
                <el-form-item>
                  <el-select v-model="basicSettings.defaultPaymentSuccessStatus" style="width: 50%" placeholder="请选择">
                    <el-option :label="t('Appointment.Settings.basic.defaultPaymentStatus.pending')" value="0" />
                    <el-option :label="t('Appointment.Settings.basic.defaultPaymentStatus.paid')" value="1" />
                    <el-option :label="t('Appointment.Settings.basic.defaultPaymentStatus.failed')" value="2" />
                  </el-select>
                </el-form-item>
              </div>

              <!-- 允许管理员在工作时间以外预约 -->
              <div class="setting-section">
                <el-form-item>
                  <el-checkbox v-model="basicSettings.allowAdminOutsideHours">
                    {{ t('Appointment.Settings.basic.allowAdmin.title') }}
                  </el-checkbox>
                </el-form-item>
              </div>
            </el-form>
          </div>

          <!-- 假期设置内容 -->
          <div v-show="activeTab === 'holiday'" class="tab-content">
            <div class="setting-section">
              <div class="setting-section-title">{{ t('Appointment.Settings.holiday.title') }}</div>
              <!-- 日期选择器和添加按钮 -->
              <div class="holiday-picker-container">
                <el-date-picker
                  v-model="holidayDateRange"
                  type="daterange"
                  size="large"
                  range-separator="–"
                  :start-placeholder="t('Appointment.Settings.holiday.startDate')"
                  :end-placeholder="t('Appointment.Settings.holiday.endDate')"
                  class="date-picker"
                  value-format="YYYY-MM-DD"
                  @change="handleDateRangeChange"
                  style="width: 400px"
                />
                <el-button type="primary" link class="add-btn" circle @click="addHolidayRange">
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>

              <!-- 已添加的假期时间列表 -->
              <div class="holiday-list" v-if="holidayRanges.length > 0">
                <div v-for="(range, index) in holidayRanges" :key="index" class="holiday-item">
                  <span>{{ range.start }} — {{ range.end }}</span>
                  <el-button type="danger" circle class="delete-btn" @click="removeHolidayRange(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 通知设置内容 -->
          <div v-show="activeTab === 'notification'" class="tab-content">
            <!-- 预约改期提醒 -->
            <div class="setting-section">
              <div class="setting-section-title">
                <span>{{ t('Appointment.Settings.notification.reschedule.title') }}</span>
                <el-switch v-model="notificationSettings.rescheduleNotification.enabled" class="ml-auto" />
              </div>
              <div v-if="notificationSettings.rescheduleNotification.enabled" class="notification-options">
                <el-select
                  v-model="notificationSettings.rescheduleNotification.methods"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="t('Appointment.Settings.notification.reschedule.placeholder')"
                  style="width: 50%"
                  filterable
                >
                  <el-option
                    v-for="template in emailTemplateOptions"
                    :key="template.id"
                    :label="template.name"
                    :value="template.code"
                  >
                    <div class="template-option">
                      <span>{{ template.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>

            <!-- 预约取消提醒 -->
            <div class="setting-section">
              <div class="setting-section-title">
                <span>{{ t('Appointment.Settings.notification.cancel.title') }}</span>
                <el-switch v-model="notificationSettings.cancelNotification.enabled" class="ml-auto" />
              </div>
              <div v-if="notificationSettings.cancelNotification.enabled" class="notification-options">
                <el-select
                  v-model="notificationSettings.cancelNotification.methods"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="t('Appointment.Settings.notification.cancel.placeholder')"
                  style="width: 50%"
                  filterable
                >
                  <el-option
                    v-for="template in emailTemplateOptions"
                    :key="template.id"
                    :label="template.name"
                    :value="template.code"
                  >
                    <div class="template-option">
                      <span>{{ template.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>

            <!-- 预约成功确认提醒 -->
            <div class="setting-section">
              <div class="setting-section-title">
                <span>{{ t('Appointment.Settings.notification.confirm.title') }}</span>
                <el-switch v-model="notificationSettings.confirmNotification.enabled" class="ml-auto" />
              </div>
              <div v-if="notificationSettings.confirmNotification.enabled" class="notification-options">
                <el-select
                  v-model="notificationSettings.confirmNotification.methods"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="t('Appointment.Settings.notification.confirm.placeholder')"
                  style="width: 50%"
                  filterable
                  >
                  <el-option
                    v-for="template in emailTemplateOptions"
                    :key="template.id"
                    :label="template.name"
                    :value="template.code"
                  >
                    <div class="template-option">
                      <span>{{ template.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>
          </div>

          
        </div>
          <!-- 按钮组 -->
        <div class="form-buttons">
          <el-button class="button-cancel" @click="handleCancel">{{ t('Appointment.Settings.buttons.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ t('Appointment.Settings.buttons.save') }}
          </el-button>
        </div>
      </div>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Delete, Calendar, Plus } from '@element-plus/icons-vue'
import { appointmentService } from '../../services/appointmentService'
import type { DateModelType } from 'element-plus'

// i18n
const { t } = useI18n()

// 路由
const router = useRouter()

// 当前激活的标签页
const activeTab = ref('basic')

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)

// 基础设置数据
const basicSettings = reactive({
  bookingLimit: 'member_only',
  saveGuestHistory: true,
  timeSlotDuration: '',
  enableCustomTimeSlots: false,
  enableAdvanceBooking: false,
  advanceBookingValue: '',
  advanceBookingUnit: '分鐘',
  advanceBookingDays: '',
  defaultBookingStatus: '',
  defaultPaymentSuccessStatus: '',
  allowAdminOutsideHours: true
})

// 假期设置数据
interface HolidayRange {
  start: string
  end: string
}

const holidayDateRange = ref<[DateModelType, DateModelType]>(['', ''])
const holidayRanges = ref<HolidayRange[]>([])

// 通知设置数据
const notificationSettings = reactive({
  enableEmailNotification: false,
  notificationEmail: '',
  emailEvents: [] as string[],
  enableSmsNotification: false,
  notificationPhone: '',
  smsEvents: [] as string[],
  rescheduleNotification: {
    enabled: false,
    methods: [] as string[]
  },
  cancelNotification: {
    enabled: false,
    methods: [] as string[]
  },
  confirmNotification: {
    enabled: false,
    methods: [] as string[]
  }
})

// 邮件模板相关状态
interface EmailTemplate {
  id: number
  name: string
  code: string
  subject: string
  content: string
  status: number
  creator_id: number
  created_at: string
  updated_at: string
}

const emailTemplateOptions = ref<EmailTemplate[]>([])
const emailTemplateLoading = {
  reschedule: ref(false),
  cancel: ref(false),
  confirm: ref(false)
}
const isEmailTemplateSelectOpen = {
  reschedule: ref(false),
  cancel: ref(false),
  confirm: ref(false)
}

// 格式化日期
const formatDate = (date: Date) => {
  if (!date) return ''
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 取消
const handleCancel = () => {
  ElMessage.info(t('Appointment.Settings.messages.cancelChanges'))
}

// 提交
const handleSubmit = async () => {
  submitLoading.value = true
  try {
    // 处理通知设置数据
    const rescheduleNotification = notificationSettings.rescheduleNotification.enabled 
      ? notificationSettings.rescheduleNotification.methods.join(',') 
      : ''
    
    const cancelNotification = notificationSettings.cancelNotification.enabled 
      ? notificationSettings.cancelNotification.methods.join(',') 
      : ''
    
    const confirmNotification = notificationSettings.confirmNotification.enabled 
      ? notificationSettings.confirmNotification.methods.join(',') 
      : ''

    // 构建提交数据
    const submitData = {
      appointment_limit: basicSettings.bookingLimit,
      non_member_record: basicSettings.saveGuestHistory,
      time_slot_interval: parseInt(basicSettings.timeSlotDuration) || 30,
      cross_time_slot: basicSettings.enableCustomTimeSlots,
      advance_booking_time: parseInt(basicSettings.advanceBookingValue) || 0,
      max_booking_days: parseInt(basicSettings.advanceBookingDays) || 30,
      default_appointment_status: parseInt(basicSettings.defaultBookingStatus) || 0,
      default_payment_status: parseInt(basicSettings.defaultPaymentSuccessStatus) || 0,
      allow_overtime_booking: false,
      allow_admin_overtime_booking: basicSettings.allowAdminOutsideHours,
      appointment_reschedule_notification: rescheduleNotification,
      appointment_cancel_notification: cancelNotification,
      appointment_confirm_notification: confirmNotification,
      holiday_settings: holidayRanges.value
    }

    // 调用保存接口
    await appointmentService.saveSettings(submitData)
    ElMessage.success(t('Appointment.Settings.messages.saveSuccess'))
    fetchSettings()
  } catch (error: any) {
    ElMessage.error(error.message || t('Appointment.Settings.messages.saveFailed'))
  } finally {
    submitLoading.value = false
  }
}

// 处理日期范围变化
const handleDateRangeChange = (val: [DateModelType, DateModelType]) => {
  if (!val) return
  holidayDateRange.value = val
}

// 添加假期范围
const addHolidayRange = () => {
  if (!holidayDateRange.value[0] || !holidayDateRange.value[1]) {
    ElMessage.warning(t('Appointment.Settings.holiday.noDate'))
    return
  }

  holidayRanges.value.push({
    start: holidayDateRange.value[0] as string,
    end: holidayDateRange.value[1] as string
  })

  // 清空选择器
  holidayDateRange.value = ['', '']
}

// 移除假期范围
const removeHolidayRange = (index: number) => {
  holidayRanges.value.splice(index, 1)
}

const fetchSettings = async () => {
  loading.value = true
  try {
    // 获取设置数据
    const response = await appointmentService.getSettings()
    if (response.data.code === 200) {
      const settings = response.data.data
      
      // 基础设置
      basicSettings.bookingLimit = settings.appointment_limit.value
      basicSettings.saveGuestHistory = settings.non_member_record.value === '1'
      basicSettings.timeSlotDuration = settings.time_slot_interval.value
      basicSettings.enableCustomTimeSlots = settings.cross_time_slot.value === '1'
      
      basicSettings.enableAdvanceBooking = settings.advance_booking_time.value > 0
      basicSettings.advanceBookingValue = settings.advance_booking_time.value
      basicSettings.advanceBookingDays = settings.max_booking_days.value
      basicSettings.defaultBookingStatus = settings.default_appointment_status.value
      basicSettings.defaultPaymentSuccessStatus = settings.default_payment_status.value
      basicSettings.allowAdminOutsideHours = settings.allow_admin_overtime_booking.value === '1'

      // 通知设置
      notificationSettings.rescheduleNotification = {
        enabled: !!settings.appointment_reschedule_notification.value,
        methods: settings.appointment_reschedule_notification.value ? settings.appointment_reschedule_notification.value.split(',') : []
      }
      notificationSettings.cancelNotification = {
        enabled: !!settings.appointment_cancel_notification.value,
        methods: settings.appointment_cancel_notification.value ? settings.appointment_cancel_notification.value.split(',') : []
      }
      notificationSettings.confirmNotification = {
        enabled: !!settings.appointment_confirm_notification.value,
        methods: settings.appointment_confirm_notification.value ? settings.appointment_confirm_notification.value.split(',') : []
      }

      // 假期设置
      try {
        holidayRanges.value = settings.holiday_settings.value ? JSON.parse(settings.holiday_settings.value) : []
      } catch (e) {
        holidayRanges.value = []
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || t('Appointment.Settings.messages.getSettingsFailed'))
  } finally {
    loading.value = false
  }
}

// 获取邮件模板列表
const fetchEmailTemplates = async (type: 'reschedule' | 'cancel' | 'confirm', keyword = '') => {
  emailTemplateLoading[type].value = true
  try {
    const params: any = {
      page: 1,
      limit: 100
    }
    
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getEmailTemplateList(params)
    if (response.data.code === 200) {
      emailTemplateOptions.value = response.data.data.items
    }
  } catch (error) {
    ElMessage.error(t('Appointment.Settings.messages.getTemplatesFailed'))
  } finally {
    emailTemplateLoading[type].value = false
  }
}

// 处理邮件模板下拉框可见性变化
const handleEmailTemplateVisibleChange = (visible: boolean, type: 'reschedule' | 'cancel' | 'confirm') => {
  if (visible) {
    isEmailTemplateSelectOpen[type].value = true
    if (emailTemplateOptions.value.length === 0) {
      fetchEmailTemplates(type)
    }
  } else {
    isEmailTemplateSelectOpen[type].value = false
  }
}

// 搜索邮件模板
const searchEmailTemplates = (query: string, type: 'reschedule' | 'cancel' | 'confirm') => {
  if (!isEmailTemplateSelectOpen[type].value) return
  
  if (query) {
    fetchEmailTemplates(type, query)
  }
}

// 初始化
onMounted(async () => {
  await fetchSettings()
  // 初始化时获取邮件模板列表
  await fetchEmailTemplates('reschedule')
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    
    h1 {
      font-size: 24px;
      font-weight: 500;
      margin: 0;
      color: #303133;
    }
  }
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      

      .setting-tabs {
        margin-bottom: 20px;
        border-bottom: 1px solid #ebeef5;
        height: auto;
        
        :deep(.el-tabs__header) {
          margin-bottom: 0;
        }
        
        :deep(.el-tabs__nav-wrap::after) {
          display: none;
        }
        
        :deep(.el-tabs__item) {
          font-size: 16px;
          color: #606266;
          padding: 0 20px;
          height: 50px;
          line-height: 50px;
          
          &.is-active {
            color: #002140;
            font-weight: 500;
          }
        }
      }

      .tab-content {
        
        .setting-section {
          margin-bottom: 30px;
          
          .setting-section-title {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 16px;
          }
          
          .ml-auto {
            margin-left: 0;
          }
          
          .ml-10 {
            margin-left: 10px;
          }
          
          .setting-input-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
          }
          
          .setting-description {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
          }
          
          /* 日历样式 */
          :deep(.el-calendar) {
            --el-calendar-cell-width: 60px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 16px;
            
            .el-calendar__header {
              padding: 12px;
            }
            
            .el-calendar__body {
              padding: 0;
            }
            
            .calendar-day {
              height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              border-radius: 4px;
              
              &.is-holiday {
                background-color: #f56c6c;
                color: #fff;
              }
              
              &:hover {
                background-color: #f5f7fa;
                
                &.is-holiday {
                  background-color: #f78989;
                }
              }
            }
          }
          
          /* 假期列表样式 */
          .holiday-list {
            width: 500px;
            .holiday-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 8px 12px;
              border-bottom: 1px solid #ebeef5;
              
              &:last-child {
                border-bottom: none;
              }
              
              .delete-btn {
                color: #909399;
                background: transparent;
                border: none;
              }
            }
          }

          .advance-booking-input-container {
            display: flex;
            align-items: center;
            margin-top: 10px;
            
            .advance-booking-input {
              width: 180px;
              margin-right: 10px;
            }
            
            .advance-unit-select {
              width: 180px;
            }
          }
          
          .setting-input-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            
            .label-text {
              width: 120px;
              color: #606266;
              font-size: 14px;
            }
          }

          .time-slot-checkbox {
            margin-top: 10px;
            margin-left: 20px;
          }
          
          .advance-booking-switch-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
          }

          .notification-options {
            margin-top: 16px;
            
            .el-select {
              :deep(.el-select__tags) {
                .el-tag {
                  background-color: #f0f2f5;
                  border-color: #e4e7ed;
                  color: #606266;
                  
                  .el-tag__close {
                    color: #909399;
                    
                    &:hover {
                      background-color: #909399;
                      color: #fff;
                    }
                  }
                }
              }
            }
          }
        }
      }

      
    }
  }
}
.form-buttons {
  display: flex;
  justify-content: center;
  margin-top: 26px;
}
.holiday-picker-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  width: 500px;

  .add-btn {
    padding: 8px;
    .el-icon {
      font-size: 16px;
    }
  }
}

// .holiday-list {
//   margin-top: 20px;
//   border: 1px solid #DCDFE6;
//   border-radius: 4px;
  
//   .holiday-item {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     padding: 12px 16px;
//     border-bottom: 1px solid #DCDFE6;
    
//     &:last-child {
//       border-bottom: none;
//     }
    
//     span {
//       color: #606266;
//       font-size: 14px;
//     }
    
//     .delete-btn {
//       padding: 6px;
      
//       .el-icon {
//         font-size: 14px;
//       }
//     }
//   }
// }

// 日期选择器弹窗样式
:deep(.el-date-picker) {
  --el-datepicker-border-color: #DCDFE6;
  --el-datepicker-inner-border-color: #E4E7ED;
  
  .el-date-range-picker__header {
    margin-bottom: 12px;
  }
  
  .el-date-range-picker__content {
    .el-date-range-picker__cell {
      &.in-range {
        background-color: #ecf5ff;
      }
      
      &.start-date,
      &.end-date {
        background-color: #409EFF;
        color: #fff;
      }
    }
  }
}

:deep(.el-switch) {
  &.is-checked {
    .el-switch__core {
      border-color: #409eff;
      background-color: #409eff;
    }
  }
  
  .el-switch__core {
    border: 1px solid #dcdfe6;
    background-color: #dcdfe6;
  }
}

:deep(.el-select-dropdown__item) {
  &.selected {
    color: #409eff;
  }
}

.template-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .template-info {
    color: #909399;
    font-size: 12px;
  }
}
</style>

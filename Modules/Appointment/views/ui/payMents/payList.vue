<template>
  <div class="bwms-module table-page">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="title"></div>
      <div class="btn-list">
        <el-button @click="handleImport" class="ml-10">
          <el-icon><img src="/resources/admin/assets/icon/UploadIcon.png" /></el-icon>
          <span>{{ t('Appointment.PaymentList.buttons_texts.import') }}</span>
        </el-button>
        <el-button @click="handleExport" class="ml-10">
          <el-icon><img src="/resources/admin/assets/icon/DownloadIcon.png" /></el-icon>
          <span>{{ t('Appointment.PaymentList.buttons_texts.export') }}</span>
        </el-button>
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="t('Cms.list.filter')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="t('Appointment.PaymentList.search.placeholder')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
        <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
        </template>
          <el-table-column prop="appointment_time" :label="t('Appointment.PaymentList.table.appointmentTime')" min-width="200" />
          <el-table-column :label="t('Appointment.PaymentList.table.customer')" width="200">
            <template #default="{ row }">
              <div class="customer-info">
                <img class="info-avatar" 
                  :src="row.customer_avatar || avatarUrl" 
                  @error="handleAvatarError"
                />
                <div class="customer-detail">
                  <div>{{ row.customer_name }}</div>
                  <div class="customer-email">{{ row.customer_email }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="staff_name" :label="t('Appointment.PaymentList.table.staffName')" width="150">
            <template #default="{ row }">
              <el-tooltip :content="row.staff_name" placement="top">
                <span>{{ row.staff_name }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="service_name" :label="t('Appointment.PaymentList.table.serviceName')" min-width="150" />
          <el-table-column prop="payment_method" :label="t('Appointment.PaymentList.table.paymentMethod')" width="150">
            <template #default="{ row }">
              {{ row.payment_method }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" :label="t('Appointment.PaymentList.table.amount')" width="150">
            <template #default="{ row }">
              <span :class="{ 'negative-amount': row.amount < 0 }">{{ formatAmount(row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payment_status" :label="t('Appointment.PaymentList.table.paymentStatus')" width="150">
            <template #default="{ row }">
              <el-tag :type="getPaymentStatusType(row.payment_status)">
                {{ row.payment_status_text }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('Appointment.PaymentList.table.operations')" width="160" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
              <div class="bwms-operate-btn" @click="handleView(row)">
                <el-icon size="16"><img src="/resources/admin/assets/icon/ViewIcon.png" /></el-icon>
              </div>
              <div class="bwms-operate-btn" @click="handleEdit(row)">
                <el-icon size="15"><img src="/resources/admin/assets/icon/EditIcon.png" /></el-icon>
              </div>
              <div class="bwms-operate-btn"
                @click="handleDeleteOne(row)"
              >
                <el-icon size="16"><img src="/resources/admin/assets/icon/DeleteIcon.png" /></el-icon>
              </div>
            </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer" style="margin-top: 0;">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      :title="t('Appointment.PaymentList.dialog.import.title')"
      width="500px"
      class="el-dialog-common-cls"
    >
      <div class="import-container">
        <el-upload
          class="upload-demo"
          drag
          action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            {{ t('Appointment.PaymentList.dialog.import.uploadTip') }}
          </div>
          <template #tip>
            <div class="el-upload__tip">
              {{ t('Appointment.PaymentList.dialog.import.fileTypeTip') }}
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <el-button type="primary" link @click="downloadTemplate">{{ t('Appointment.PaymentList.dialog.import.downloadTemplate') }}</el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">{{ t('Appointment.PaymentList.buttons.cancel') }}</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploading"
            :disabled="!selectedFile"
          >
            {{ t('Appointment.PaymentList.buttons.confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 付款详情抽屉 -->
    <detail-drawer
      v-model:visible="detailDrawerVisible"
      :payment-id="currentPaymentId"
      :detail-data="currentRowData"
      @refresh="fetchData"
      @edit="handleEdit"
    />

    <!-- 编辑抽屉 -->
    <edit-drawer
      v-model:visible="editDrawerVisible"
      :payment-id="currentEditPaymentId"
      :detail-data="currentEditRowData"
      @refresh="fetchData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { env, getBaseUrl } from '/admin/support/helper'
import { Search, View, Edit, Upload, Download, UploadFilled, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { appointmentService } from '../../services/appointmentService'
import DetailDrawer from './detailDrawer.vue'
import EditDrawer from './editDrawer.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const baseURL = getBaseUrl()

// 定义付款状态枚举
enum PaymentStatus {
  PENDING = 0,    // 未支付
  PAID = 1,       // 已支付
  CANCELLED = 2,  // 支付失败
}

// 路由
const router = useRouter()

// 加载状态
const loading = ref(false)

// 导入对话框
const importDialogVisible = ref(false)
const importLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  payment_status: undefined,
  date_range: undefined
})

// 表格数据
const tableData = ref([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 4 // 示例数据总数
})

// 详情抽屉
const detailDrawerVisible = ref(false)
const currentPaymentId = ref<number | null>(null)

// 当前选中的行数据
const currentRowData = ref<Record<string, any> | null>(null)

// 编辑抽屉
const editDrawerVisible = ref(false)
const currentEditPaymentId = ref<number | null>(null)
const currentEditRowData = ref<Record<string, any> | null>(null)

// 添加上传相关的变量
const selectedFile = ref()
const uploading = ref(false)

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 获取付款状态标签类型
const getPaymentStatusType = (status: number) => {
  const types: Record<number, string> = {
    [PaymentStatus.PENDING]: 'warning',
    [PaymentStatus.PAID]: 'success',
    [PaymentStatus.CANCELLED]: 'danger'
  }
  return types[status] || ''
}

// 格式化金额
const formatAmount = (amount: string) => {
  return parseFloat(amount).toFixed(2)
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.payment_status = undefined
  searchForm.date_range = undefined
  showFilterDropdown.value = false
  handleSearch()
}

// 删除服务
const handleDeleteOne = (row: any) => {
  ElMessageBox.confirm(
    t('Appointment.PaymentList.messages.deleteConfirm'),
    t('Appointment.PaymentList.messages.deleteTitle'),
    {
      confirmButtonText: t('Appointment.PaymentList.buttons.confirm'),
      cancelButtonText: t('Appointment.PaymentList.buttons.cancel'),
      type: 'warning'
    }
  ).then(async () => {
    try {
      await appointmentService.deleteAppointment(row.id)
      ElMessage.success(t('Appointment.PaymentList.messages.deleteSuccess'))
      fetchData()
    } catch (error) {
      ElMessage.error(t('Appointment.PaymentList.messages.deleteFailed'))
    }
  })
}

// 处理导入
const handleImport = () => {
  importDialogVisible.value = true
  selectedFile.value = undefined
}

// 处理导出
const handleExport = async () => {
  try {
    loading.value = true
    const exportUrl = `${baseURL}reservation/appointments/export?page=${pagination.page}&limit=${pagination.pageSize}`
    const link = document.createElement('a')
    link.href = exportUrl
    link.setAttribute('download', `付款记录_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success(t('Appointment.PaymentList.messages.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('Appointment.PaymentList.messages.exportFailed'))
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = (row: any) => {
  currentPaymentId.value = row.id
  currentRowData.value = row
  detailDrawerVisible.value = true
}

// 编辑付款记录
const handleEdit = (row: any) => {
  currentEditPaymentId.value = row.id
  currentEditRowData.value = row
  editDrawerVisible.value = true
}

// 下载导入模板
const downloadTemplate = () => {
  try {
    const link = document.createElement('a')
    link.href = `${baseURL}reservation/appointments/export-template`
    link.download = '付款记录导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    ElMessage.error(t('Appointment.PaymentList.messages.downloadFailed'))
  }
}

// 文件选择改变
const handleFileChange = (uploadFile: any) => {
  selectedFile.value = uploadFile
}

// 处理上传按钮点击
const handleUpload = async () => {
  if (!selectedFile.value?.raw) {
    ElMessage.warning(t('Appointment.PanelList.messages.selectFile'))
    return
  }

  uploading.value = true
  try {
    const formData = new FormData()
    formData.append('file', selectedFile.value.raw)
    
    const { data } = await appointmentService.importAppointments(formData)
    
    if (data.code === 200) {
      ElMessage.success(t('Appointment.PaymentList.messages.importSuccess'))
      importDialogVisible.value = false
      fetchData()
    } else {
      ElMessage.error(data.message || t('Appointment.PaymentList.messages.importFailed'))
    }
  } catch (error) {
  } finally {
    uploading.value = false
  }
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      keyword: searchForm.keyword,
      payment_status: searchForm.payment_status,
      start_date: searchForm.date_range?.[0],
      end_date: searchForm.date_range?.[1]
    }
    
    const { data } = await appointmentService.getAppointmentLists(params)
    
    if (data.code === 200 && data.data) {
      tableData.value = data.data.items.map((item: any) => ({
        id: item.id,
        appointment_id: item.id,
        appointment_time: item.appointment_date,
        customer_name: item.customer?.name || '--',
        customer_email: item.customer?.email || '--',
        customer_avatar: item.customer?.photo || '',
        staff_name: item.service?.staffs?.map((staff: any) => staff.name).join(', ') || '--',
        service_name: item.service?.name || '--',
        payment_method: item.payment_method || '--',
        amount: item.final_price,
        payment_status: item.payment_status,
        payment_status_text: item.payment_status_text,
        created_at: item.created_at,
        updated_at: item.updated_at,
        location: item.location,
        original_price: item.original_price,
        discount_amount: item.discount_amount,
        final_price: item.final_price,
        remark: item.remark
      }))
      pagination.total = data.data.total || 0
    } else {
      tableData.value = []
      pagination.total = 0
      ElMessage.warning(data.message || t('Appointment.PaymentList.messages.getListFailed'))
    }
  } catch (error) {
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 在 script setup 部分添加
const avatarUrl = ref('https://cube.elemecdn.com/3/7c/********************************.png')

// 添加头像加载错误处理函数
const handleAvatarError = (e: Event) => {
  const imgElement = e.target as HTMLImageElement
  imgElement.src = avatarUrl.value
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
 
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 允许内容区域滚动

      .search-area {
        flex-shrink: 0; // 防止搜索区域被压缩
        position: relative;
        
        .el-form {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }
      }
      
      .el-table {
        margin-bottom: 20px; // 添加底部边距，与分页保持间隔

        .customer-info {
          display: flex;
          align-items: center;
          
          .info-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            background-color: #f5f7fa;
          }
          
          .customer-detail {
            margin-left: 10px;
            
            .customer-email {
              font-size: 12px;
              color: #909399;
              margin-top: 3px;
            }
          }
        }
        
        .negative-amount {
          color: #F56C6C;
        }
      }
    }
  }
}

// 分页样式调整
.pagination {
  margin-top: 20px;
  
  .page-size-text {
    font-size: 14px;
    color: #606266;
  }
  
  .total-text {
    font-size: 14px;
    color: #606266;
    margin-left: 10px;
  }
}

// 导入容器样式
.import-container {
  padding: 10px;
  
  .template-download {
    margin-top: 15px;
  }
}

.discount-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .discount-info {
    color: #909399;
    font-size: 12px;
  }
}

.discount-type-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}
</style>

<template>
  <el-drawer
    v-model="drawerVisible"
    :title="t('Appointment.PaymentList.edit.title')"
    :size="500"
    :with-header="true"
    destroy-on-close
    direction="rtl"
    custom-class="edit-drawer"
  >
    <template #header>
      <div class="drawer-header">
        <h4 class="drawer-title">{{ t('Appointment.PaymentList.edit.title') }}</h4>
      </div>
    </template>

    <div class="payment-detail-container">
      <div class="payment-content">
        <el-form 
          ref="formRef" 
          :model="formData" 
          :rules="rules" 
          label-width="120px"
          label-position="top"
          class="payment-form"
        >
          <!-- 服务价格 -->
          <el-form-item :label="t('Appointment.PaymentList.table.amount')" prop="service_price">
            <el-input
              v-model.number="formData.service_price"
              type="number"
              :disabled="true"
              style="width: 100%"
            >
            </el-input>
          </el-form-item>

          <!-- 付款方式 -->
          <el-form-item :label="t('Appointment.PaymentList.edit.method')" prop="payment_method">
            <el-input
              v-model="formData.payment_method"
              :placeholder="t('Appointment.PaymentList.edit.method')"
              style="width: 100%"
            >
            </el-input>
          </el-form-item>

          <!-- 应用优惠 -->
          <el-form-item :label="t('Appointment.PaymentList.edit.discount')" prop="discount">
            <el-select 
              v-model="formData.discount_code" 
              :placeholder="t('Appointment.PaymentList.edit.discount')"
              style="width: 100%"
              @visible-change="handleDiscountVisibleChange"
            >
              <el-option
                v-for="item in discountOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              <div class="discount-option flex justify-between">
                <span>{{ item.name }}</span>
                <span class="discount-info">{{ getDiscountText(item) }}</span>
              </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 折扣金額 -->
          <el-form-item :label="t('Appointment.PaymentList.edit.discountAmount')" prop="discount">
            <el-input
              v-model.number="formData.discount"
              type="number"
              :disabled="true"
              style="width: 100%"
              :step="0.01"
              :precision="2"
            >
            </el-input>
          </el-form-item>

          <!-- 支付金额 -->
          <el-form-item :label="t('Appointment.PaymentList.edit.amount')" prop="amount">
            <el-input
              v-model.number="formData.amount"
              type="number"
              :disabled="true"
              style="width: 100%"
              :step="0.01"
              :precision="2"
            >
            </el-input>
          </el-form-item>

          <!-- 支付状态 -->
          <el-form-item :label="t('Appointment.PaymentList.edit.status')" prop="payment_status">
            <el-select 
              v-model="formData.payment_status" 
              :placeholder="t('Appointment.PaymentList.edit.status')"
              style="width: 100%"
            >
              <el-option
                v-for="item in paymentStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 底部按钮 -->
      <div class="drawer-footer">
        <el-button @click="handleCancel">{{ t('Appointment.PaymentList.buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">{{ t('Appointment.PaymentList.buttons.submit') }}</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { appointmentService } from '../../services/appointmentService'
import { useI18n } from 'vue-i18n'

// 使用i18n
const { t } = useI18n()

// 定义属性
const props = defineProps({
  paymentId: {
    type: [Number, String, null],
    required: false,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['update:visible', 'refresh'])

// 付款状态枚举
enum PaymentStatus {
  PENDING = 0,    // 未支付
  PAID = 1,       // 已支付
  CANCELLED = 2,  // 支付失败
}

// 创建内部可控制的抽屉状态
const drawerVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 加载和保存状态
const loading = ref(false)
const saving = ref(false)

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
interface FormData {
  service_price: number;
  discount_code: string;
  discount: number;
  amount: number;
  payment_status: number;
  payment_method: string;
  remark: string;
}

const formData = ref<FormData>({
  service_price: 0,
  discount_code: '',
  discount: 0,
  amount: 0,
  payment_status: 1,
  payment_method: '',
  remark: ''
});

// 表单验证规则
const rules = reactive<FormRules>({
  amount: [
    { required: false, message: t('Appointment.PaymentList.edit.amount'), trigger: 'blur' }
  ],
  payment_status: [
    { required: false, message: t('Appointment.PaymentList.edit.status'), trigger: 'change' }
  ]
})

// 付款状态选项
const paymentStatusOptions = [
  { value: PaymentStatus.PENDING, label: t('Appointment.PaymentList.detail.status.pending') },
  { value: PaymentStatus.PAID, label: t('Appointment.PaymentList.detail.status.paid') },
  { value: PaymentStatus.CANCELLED, label: t('Appointment.PaymentList.detail.status.failed') }
]

// 折扣相关状态
const discountLoading = ref(false)
const discountSearchQuery = ref('')
const isDiscountSelectOpen = ref(false)
const discountOptions = ref<Array<{
  id: number,
  name: string,
  type: string,
  value: string,
}>>([])

// 获取折扣列表
const fetchDiscounts = async (keyword = '') => {
  discountLoading.value = true
  try {
    const params: any = {
      keyword: ''
    }
    
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim()
    }
    
    const response = await appointmentService.getDiscountList(params)
    if (response.data.code === 200) {
      discountOptions.value = response.data.data.items.map((item: any) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        value: item.value
      }))
    }
  } catch (error) {
    ElMessage.error(t('Appointment.PaymentList.messages.getListFailed'))
  } finally {
    discountLoading.value = false
  }
}

// 处理折扣下拉框可见性变化
const handleDiscountVisibleChange = (visible: boolean) => {
  if (visible) {
    isDiscountSelectOpen.value = true
    if (discountOptions.value.length === 0) {
      fetchDiscounts()
    }
  } else {
    isDiscountSelectOpen.value = false
    discountSearchQuery.value = ''
    
    setTimeout(() => {
      if (!isDiscountSelectOpen.value) {
        fetchDiscounts()
      }
    }, 200)
  }
}

// 远程搜索折扣
const searchDiscounts = (query: string) => {
  if (!isDiscountSelectOpen.value) return
  
  discountSearchQuery.value = query
  if (query) {
    fetchDiscounts(query)
  }
}

// 监听折扣代码变化
watch(() => formData.value.discount_code, async (newVal) => {
  if (!newVal) {
    formData.value.discount = 0;
    return;
  }
  
  try {
    const discount = discountOptions.value.find(item => item.id === Number(newVal));
    if (discount) {
      if (discount.type === 'fixed') {
        formData.value.discount = Number(parseFloat(discount.value).toFixed(2));
      } else if (discount.type === 'percentage') {
        formData.value.discount = Number((formData.value.service_price * (parseFloat(discount.value) / 100)).toFixed(2));
      }
    }
  } catch (error) {
  }
  
  // 重新计算总金额
  calculateTotal();
});

// 计算总金额
const calculateTotal = () => {
  const servicePrice = parseFloat(formData.value.service_price.toString()) || 0;
  const discount = parseFloat(formData.value.discount.toString()) || 0;
  formData.value.amount = Number((servicePrice - discount).toFixed(2));
};

// 监听服务价格变化
watch(() => formData.value.service_price, () => {
  calculateTotal();
});

// 监听折扣变化
watch(() => formData.value.discount, () => {
  calculateTotal();
});

// 初始化表单数据
const initFormData = () => {
  if (props.detailData) {
    formData.value.service_price = Number(parseFloat(props.detailData.original_price || 0).toFixed(2))
    formData.value.discount = Number(parseFloat(props.detailData.discount_amount || 0).toFixed(2))
    formData.value.discount_code = props.detailData.discount_code || ''
    formData.value.amount = Number(parseFloat(props.detailData.final_price || 0).toFixed(2))
    formData.value.payment_status = props.detailData.payment_status
    formData.value.payment_method = props.detailData.payment_method || ''
  }
}
// 在弹窗中显示优惠类型和值
const getDiscountText = (discount: any) => {
  if (!discount) return ''
  if (discount.type === 'percentage') {
    return `${discount.value / 100} 折`
  } else if (discount.type === 'fixed') {
    return `固定優惠 ${discount.value} 元`
  }
  return ''
}
// 监听可见性变化，初始化数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      initFormData()
      fetchDiscounts()
    }
  }
)

// 取消编辑
const handleCancel = () => {
  drawerVisible.value = false
}

// 保存数据
const handleSave = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    saving.value = true
    try {
      const data = {
        discount_code: formData.value.discount_code,
        final_price: formData.value.amount,
        payment_status: formData.value.payment_status,
        payment_method: formData.value.payment_method
      }
      
      let response
      if (props.paymentId) {
        response = await appointmentService.updatePayment(Number(props.paymentId), data)
      } else {
        ElMessage.error(t('Appointment.PaymentList.edit.saveFailed'))
        return
      }
      
      if (response.data.code === 200) {
        ElMessage.success(t('Appointment.PaymentList.edit.saveSuccess'))
        drawerVisible.value = false
        emit('refresh')
      } else {
        ElMessage.error(response.data.message || t('Appointment.PaymentList.edit.saveFailed'))
      }
    } catch (error) {
    } finally {
      saving.value = false
    }
  })
}

</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .drawer-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}
.discount-option {
  align-items: center;
  width: 100%;
  
  .discount-info {
    color: #909399;
    font-size: 12px;
  }
}
.payment-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  
  .payment-content {
    flex: 1;
    overflow-y: auto;
    margin-top: -20px;
    padding-bottom: 0;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(64, 158, 255, 0.3);
      border-radius: 3px;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background-color: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }
  }
  
  
  .drawer-footer {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    
  }
}

// 添加动画效果
:deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-drawer.rtl) {
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}
</style>

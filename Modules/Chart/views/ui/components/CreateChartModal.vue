<template>
  <div class="chart-header">
    <el-input v-model="chartName" :placeholder="$t('Chart.create.name_placeholder')" class="chart-name-input" />
    <el-select v-model="selectedLibrary" :placeholder="$t('Chart.create.library_placeholder')" class="chart-library-select">
      <el-option v-for="lib in chartLibraries" :key="lib.value" :label="lib.label" :value="lib.value" />
    </el-select>
  </div>

  <div class="chart-type-list">
    <template v-if="loading">
      <div class="type-list-wrapper">
        <div v-for="i in 8" :key="i" class="type-item">
          <el-skeleton-item variant="image" style="width: 100%; height: 140px" />
          <el-skeleton-item variant="text" style="margin-top: 10px; height: 20px" />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="type-list-wrapper">
        <div v-for="chartType in currentLibraryTypes" :key="chartType.id" class="type-item" :class="{ active: chartType.id === checkedType?.id }" @click="checkedType = chartType">
          <div class="pic-box">
            <div class="pic">
              <el-image :src="chartType.image" fit="contain" class="img" loading="lazy">
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="checked-bg">
              <el-icon size="24" color="#5585C8"><Check /></el-icon>
            </div>
          </div>
          <div class="type-text">{{ chartType.name }}</div>
        </div>
      </div>
    </template>
  </div>

  <div class="flex justify-center" style="margin-top: 20px;">
    <el-button :disabled="!isValid" type="primary" @click="goPage">{{ $t('Chart.list.btn_text2') }}</el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import ChartService from '../../application/services/ChartService'
import ChartRepositoryImpl from '../../infrastructure/repositories/ChartRepositoryImpl'
import Message from '/admin/support/message'

const { t } = useI18n()

const chartTypeList = ref<{ [key: string]: any[] }>({})
const currentLibraryTypes = computed(() => {
  return selectedLibrary.value ? chartTypeList.value[selectedLibrary.value] || [] : []
})

const checkedType = ref<any>(null)
const router = useRouter()
const loading = ref<Boolean>(true)

const chartName = ref('')
const selectedLibrary = ref('echarts')

const chartLibraries = [
  { label: 'Chart.js', value: 'chartjs' },
  { label: 'ECharts', value: 'echarts' },
  { label: 'Google Charts', value: 'google-chart' },
]

const fetchChartTypes = async () => {
  try {
    loading.value = true
    const chartRepositoryImpl = new ChartRepositoryImpl()
    const chartService = new ChartService(chartRepositoryImpl)
    chartTypeList.value = await chartService.getChartLibrary()
  } catch (error) {
    Message.error(t('Chart.create.fetch_error'))
    console.error('Failed to fetch chart types:', error)
  } finally {
    loading.value = false
  }
}

// 监听库选择变化，重置已选图表类型
watch(selectedLibrary, () => {
  checkedType.value = null
})

const isValid = computed(() => {
  return chartName.value.trim() && selectedLibrary.value && checkedType.value?.id
})

const goPage = () => {
  if (isValid.value) {
    router.push({
      name: 'chartCreate',
      query: {
        chartTypeId: checkedType.value.id,
        chartName: chartName.value,
        library: selectedLibrary.value,
      },
    })
  }
}

onMounted(() => {
  fetchChartTypes()
})
</script>

<style lang="scss" scoped>
.chart-header {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 0 10px;

  .chart-name-input {
    flex: 1;
  }

  .chart-library-select {
    width: 200px;
  }
}

.chart-type-list {
  margin: 0 -10px;
  height: 420px;
  position: relative;

  .type-list-wrapper {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    padding: 0 10px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #5585C8;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #4A75B3;
    }
  }

  .type-item {
    padding: 8px;
    width: 25%;
    height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;

    .pic-box {
      border: 2px solid transparent;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      height: 140px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;

      .pic {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;

        .img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }

        .image-error {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;
          font-size: 24px;
        }
      }

      .checked-bg {
        opacity: 0;
        background-color: rgba(85, 133, 200, 0.1);
        position: absolute;
        inset: 0;
        z-index: 1;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          transform: scale(0);
          transition: transform 0.3s ease;
        }
      }

      &:hover {
        border-color: #5585C8;
        transform: translateY(-2px);
      }
    }

    .type-text {
      margin-top: 8px;
      text-align: center;
      font-size: 14px;
      color: #1D2129;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.active {
      .pic-box {
        border-color: #5585C8;

        .checked-bg {
          opacity: 1;

          .el-icon {
            transform: scale(1);
          }
        }
      }
    }
  }
}

</style>

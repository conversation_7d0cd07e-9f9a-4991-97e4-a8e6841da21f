<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      <div class="btn-list">
        <el-button @click="openCreateChartModal" type="primary">
          <el-icon size="16"><img src="/resources/admin/assets/icon/PlusIcon.png" /></el-icon>
          <span>{{ $t('Chart.list.btn_text3') }}</span>
        </el-button>
      </div>
    </div>

    <div class="module-con">
      <div class="box">
        <el-table
          v-loading="loading"
          :data="pagedCharts"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" :label="t('Chart.list.table_th1')" min-width="200" />
          <el-table-column :label="t('Chart.list.table_th2')" min-width="120">
            <template #default="scope">
              {{ $t(`Chart.list.chart_types.${scope.row.chart_type}`) }}
            </template>
          </el-table-column>
          <el-table-column prop="source_type" :label="t('Chart.list.table_th3')" min-width="120" />
          <el-table-column :label="t('Chart.list.table_th4')" min-width="180">
            <template #default="scope">
              <div class="short-code" @click.stop>
                <el-tooltip effect="dark" content="Click To Copy" placement="top">
                  <div class="copy-btn" @click="copyTextToClipboard(scope.row.shortCode)">
                    <el-icon size="18" color="#121212"><DocumentCopy /></el-icon>
                  </div>
                </el-tooltip>
                <div class="inp-box">
                  <input type="text" readonly v-model="scope.row.shortCode" @focus="selectAllText" />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="creator" :label="t('Chart.list.table_th5')" min-width="120" />
          <el-table-column :label="t('Chart.list.table_th6')" min-width="100">
            <template #default="scope">
              {{ $t(`Chart.list.status.${scope.row._status === '1' ? 'active' : 'inactive'}`) }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" :label="t('Chart.list.table_th7')" min-width="160" />
          <el-table-column :label="t('Chart.list.actions.title')" fixed="right" width="150">
            <template #default="scope">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn" @click="goToChartDetails(scope.row)">
                  <el-icon size="15"><img src="/resources/admin/assets/icon/EditIcon.png" /></el-icon>
                </div>
                <div class="bwms-operate-btn" @click="handleDelete(scope.row)">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/DeleteIcon.png" /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="box-footer">
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Chart.common.pagination.page_size_text') }}</span>
            <el-select
              v-model="pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Chart.common.pagination.total_items', { total: charts.length }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="currentPage"
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :total="charts.length"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="showCreateChartModal" :title="t('Chart.list.dialog_tit')" width="1080" class="el-dialog-common-cls" :close-on-click-modal="false" :close-on-press-escape="false" destroy-on-close>
      <CreateChartModal />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import ChartRepositoryImpl from '../../infrastructure/repositories/ChartRepositoryImpl'
import ChartService from '../../application/services/ChartService.ts'
import CreateChartModal from './CreateChartModal.vue'
import Message from '/admin/support/message'
import Chart from '../../domain/models/Chart.ts'
import { ChartEngine } from '../../domain/models/ChartType'
import { ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

// 实例化 ChartRepositoryImpl 和 ChartService
const chartRepository = new ChartRepositoryImpl()
const chartService = new ChartService(chartRepository)

const charts = ref<Chart[]>([])
const selectedCharts = ref<Chart[]>([])
const showCreateChartModal = ref(false)
const router = useRouter()

const currentPage = ref(1)
const pageSize = ref(10)

const loading = ref(false)

const pagedCharts = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return charts.value.slice(startIndex, endIndex)
})

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const fetchCharts = async () => {
  loading.value = true
  try {
    charts.value = await chartService.getCharts()
  } catch (error) {
    Message.error('Failed to fetch charts')
  } finally {
    loading.value = false
  }
}

const openCreateChartModal = () => {
  showCreateChartModal.value = true
}

const closeCreateChartModal = () => {
  showCreateChartModal.value = false
}

const goToChartDetails = (row: Chart) => {
  // 处理数据，标准化 engine 类型
  const chartData = {
    ...row,
    engine: row.engine.toLowerCase().replace(/[\s-]/g, '') === 'echarts' ? 'echarts' : row.engine,
    options: typeof row.options === 'string' ? JSON.parse(row.options) : row.options,
    source: typeof row.source === 'string' ? JSON.parse(row.source) : row.source,
  }

  // 将数据存储到 sessionStorage
  sessionStorage.setItem('editChartData', JSON.stringify(chartData))

  // 简化路由参数
  router.push({
    name: 'chartCreate',
    query: {
      id: row.id,
      isEdit: 'true',
    },
  })
}

const handleDelete = async (chart: Chart) => {
  try {
    await ElMessageBox.confirm(t('Chart.list.delete_confirm'), t('Chart.list.delete_title'), {
      confirmButtonText: t('Chart.list.delete_confirm_btn'),
      cancelButtonText: t('Chart.list.delete_cancel_btn'),
      type: 'warning',
    })
    await chartService.deleteCharts([chart.id])
    Message.success(t('Chart.list.delete_success'))
    fetchCharts()
  } catch (error) {
    if (error !== 'cancel') {
      Message.error(t('Chart.list.delete_error'))
    }
  }
}

const copyTextToClipboard = text => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      Message.success('Text copied to clipboard')
    })
    .catch(err => {
      Message.error('Failed to copy text')
    })
}

const selectAllText = event => {
  event.target.select()
}

onMounted(() => {
  fetchCharts()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh;
  
  .module-con {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}

.short-code {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin: 5px 0;
  background-color: #f5f7fa;
  overflow: hidden;
  display: flex;
  width: 100%;

  .copy-btn {
    border-right: 1px solid #e4e7ed;
    padding: 8px;
    width: 32px;
    background-color: #f5f7fa;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s;

    &:hover {
      background-color: #ecf5ff;
    }
  }

  .inp-box {
    background-color: #f5f7fa;
    text-align: center;
    flex-grow: 1;
    min-width: 10%;

    input {
      outline: none;
      border: none;
      padding: 8px;
      background-color: transparent;
      width: 100%;
      height: 100%;
      font-size: 13px;
      color: #606266;

      &:focus {
        outline: none;
      }
    }
  }
}

</style>

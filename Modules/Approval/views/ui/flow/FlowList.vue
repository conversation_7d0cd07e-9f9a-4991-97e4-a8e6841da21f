<template>
  <div class="bwms-module table-page">
  

    <!-- 头部区域 -->
    <div class="module-header">
      <!-- 筛选 -->
      <FilterPopover v-model="showFilterDropdown">
        <template #reference>
          <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
            <el-icon>
              <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
            </el-icon>
            <span>{{ $t('Cms.list.filter') }}</span>
          </el-button>
        </template>
        <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
          <el-form-item :label="$t('Approval.FlowList.namePlaceholder')" style="width: 100%;">
            <el-input 
              v-model="searchForm.keyword"
              :placeholder="$t('Approval.FlowCreate.namePlaceholder')"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="flex justify-center">
            <el-button class="el-button-default" @click="handleReset">
              <el-icon><Refresh /></el-icon>
              <span>{{ $t('Cms.list.refresh') }}</span>
            </el-button>
            <el-button class="button-no-border" type="primary" @click="handleSearch">
              <el-icon><Filter /></el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </div>
        </template>
      </FilterPopover>

        <el-button type="primary" @click="handleCreate">
          <el-icon><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
          <span>{{ $t('Approval.FlowList.create') }}</span>
        </el-button>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        <!-- 表格区域 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="name" :label="$t('Approval.FlowList.name')" min-width="150" />
          <el-table-column prop="scope" :label="$t('Approval.FlowList.scope')" min-width="150" />
          <el-table-column prop="created_at" :label="$t('Approval.FlowList.createdAt')" width="180" />
          <el-table-column prop="updated_at" :label="$t('Approval.FlowList.updatedAt')" width="180" />
          <el-table-column prop="is_enabled" :label="$t('Approval.FlowList.status')" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.is_enabled"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('Approval.FlowList.operations')" width="200" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn"
                    @click="handleEdit(row)"
                  >
                    <el-icon size="16"><img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" /></el-icon>
                  </div>
                  <div class="bwms-operate-btn"
                    @click="handleCopy(row)"
                  >
                    <el-icon size="16"><img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" /></el-icon>
                  </div>
                  <div class="bwms-operate-btn"
                    @click="handleDelete(row)"
                  >
                    <el-icon size="16"><img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" /></el-icon>
                </div>
                <div class="bwms-operate-btn"
                  @click="handleView(row)"
                >
                  <el-icon size="16"><img src="/resources/admin/assets/icon/ViewIcon.png" alt="ViewIcon" /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="currentPage"
              background
              layout="prev, pager, next"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Plus, Edit, CopyDocument, Delete, More, View, Search, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ISearchForm, IFlow } from '../../types/index'
import { approvalService } from '../../services/approvalService'

const router = useRouter()
const { t } = useI18n()

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 搜索表单
const searchForm = reactive<ISearchForm>({
  keyword: '',
  page: 1,
  limit: 10,
  sort_field: 'created_at',
  sort_order: 'desc'
})

// 适用范围选项
const scopeOptions = [
  { label: t('Approval.FlowList.scopeOptions.product'), value: 'product' },
  { label: t('Approval.FlowList.scopeOptions.whitepaper'), value: 'whitepaper' },
  { label: t('Approval.FlowList.scopeOptions.news'), value: 'news' }
]

// 表格数据
const loading = ref(false)
const tableData = ref<IFlow[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: ISearchForm = {
      page: currentPage.value,
      limit: pageSize.value,
      sort_field: searchForm.sort_field,
      sort_order: searchForm.sort_order
    }
    
    if (searchForm.keyword?.trim()) {
      params.keyword = searchForm.keyword.trim()
    }

    const { data } = await approvalService.getList(params)
    
    if (data.code === 200) {
      tableData.value = data.data.items
      total.value = data.data.total
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowList.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  searchForm.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 重置
const handleReset = () => {
  // 重置搜索表单
  searchForm.keyword = ''
  searchForm.sort_field = 'created_at'
  searchForm.sort_order = 'desc'
  
  // 重置分页
  currentPage.value = 1
  pageSize.value = 10
  
  // 关闭下拉框
  showFilterDropdown.value = false
  
  // 重新获取数据
  fetchData()
}

// 分页
const handleSizeChange = (val: number) => {
  pageSize.value = val
  searchForm.limit = val
  fetchData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  searchForm.page = val
  fetchData()
}

// 创建流程
const handleCreate = () => {
  router.push('/approval/flows/create')
}

// 查看详情
const handleView = (row: IFlow) => {
  router.push(`/approval/flows/${row.id}`)
}

// 编辑流程
const handleEdit = (row: IFlow) => {
  router.push(`/approval/flows/${row.id}/edit`)
}

// 复制流程
const handleCopy = async (row: IFlow) => {
  try {
    const { data } = await approvalService.duplicate(row.id)
    if (data.code === 200) {
      ElMessage.success(t('Approval.FlowList.copySuccess'))
      fetchData() // 刷新列表
    } else {
      ElMessage.error(data.message || t('Approval.FlowList.copyFailed'))
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowList.copyFailed'))
  }
}

// 删除流程
const handleDelete = (row: IFlow) => {
  ElMessageBox.confirm(
    t('Approval.FlowList.confirmDelete'),
    t('Approval.FlowList.tip'),
    {
      confirmButtonText: t('Approval.FlowList.confirm'),
      cancelButtonText: t('Approval.FlowList.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      const { data } = await approvalService.delete(row.id)
      if (data.code === 200) {
        ElMessage.success(t('Approval.FlowList.deleteSuccess'))
        fetchData() // 刷新列表
      } else {
        ElMessage.error(data.message || t('Approval.FlowList.deleteFailed'))
      }
    } catch (error) {
      ElMessage.error(t('Approval.FlowList.deleteFailed'))
    }
  }).catch(() => {
    ElMessage.info(t('Approval.FlowList.deleteCancel'))
  })
}

// 切换状态
const handleStatusChange = async (row: IFlow) => {
  try {
    await approvalService.switchStatus(row.id)
    ElMessage.success(row.is_enabled === 1 ? t('Approval.FlowList.enabled') : t('Approval.FlowList.disabled'))
  } catch (error) {
    row.is_enabled = row.is_enabled === 1 ? 0 : 1
    ElMessage.error(t('Approval.FlowList.statusUpdateFailed'))
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding-top: 20px;
      

    }
  }

  .el-dropdown {
    margin-left: 8px;
  }

  .el-button [class*=el-icon] + span {
    margin-left: 0;
  }
}
</style> 
<template>
  <div class="table-page bwms-module">
    <div class="module-header">
      </div>
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="scroll-bar-custom-transparent">
        <div class="box ">
          <el-form 
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-position="top"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3>{{ $t('Approval.FlowCreate.basicInfo') }}</h3>
              <el-form-item :label="$t('Approval.FlowCreate.name')" prop="name">
                <el-input 
                  v-model="formData.name"
                  :placeholder="$t('Approval.FlowCreate.namePlaceholder')"
                  style="width: 50%"
                />
              </el-form-item>
              
              <el-form-item :label="$t('Approval.FlowCreate.scope')" prop="scope">
                <el-input 
                  v-model="formData.scope"
                  :placeholder="$t('Approval.FlowCreate.scopePlaceholder')"
                  style="width: 50%"
                />
              </el-form-item>

              <el-form-item :label="$t('Approval.FlowCreate.description')" prop="description">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="4"
                  :placeholder="$t('Approval.FlowCreate.descriptionPlaceholder')"
                  style="width: 50%"
                />
              </el-form-item>
            </div>

            <!-- 步骤配置 -->
            <div class="form-section">
              <div class="section-header">
                <h3>{{ $t('Approval.FlowCreate.steps') }}</h3>
                <el-button type="primary" @click="addStep">
                  <el-icon size="16"><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
                  <span>{{ $t('Approval.FlowCreate.addStep') }}</span>
                </el-button>
              </div>

              <!-- 第一步(草稿)单独展示 -->
              <el-collapse v-model="activeSteps">
                <el-collapse-item 
                  :name="0"
                  class="step-item"
                >
                  <template #title>
                    <div class="step-header">
                      <div class="step-title">
                        <div class="step-info">
                          <div class="step-dot" :style="{ backgroundColor: getStepColor(0) }" />
                          <span>{{ formData.steps[0].name || $t('Approval.FlowCreate.step', { num: 1 }) }}</span>
                        </div>
                      </div>
                    </div>
                  </template>

                  <div class="step-content">
                    <el-form-item 
                      :label="$t('Approval.FlowCreate.stepName')" 
                      prop="steps.0.name"
                    >
                      <el-input 
                        v-model="formData.steps[0].name"
                        :placeholder="$t('Approval.FlowCreate.stepNamePlaceholder')"
                        style="width: 50%"
                      />
                    </el-form-item>

                    <el-form-item :label="$t('Approval.FlowCreate.stepCode')" prop="steps.0.stepCode">
                      <el-input 
                        v-model="formData.steps[0].stepCode"
                        :placeholder="$t('Approval.FlowCreate.stepCodePlaceholder')"
                        style="width: 50%"
                      />
                    </el-form-item>

                    <el-form-item :label="$t('Approval.FlowCreate.approvalRoles')" prop="steps.0.groupIds">
                      <el-select
                        v-model="formData.steps[0].groupIds"
                        multiple
                        collapse-tags
                        filterable
                        remote
                        :remote-method="remoteSearch"
                        :loading="groupLoading"
                        :placeholder="$t('Approval.FlowCreate.selectRoles')"
                        style="width: 50%"
                        clearable
                        @visible-change="handleVisibleChange"
                        @change="handleGroupIdsChange($event, 0)"
                      >
                        <el-option
                          v-for="group in groupOptions"
                          :key="group.value"
                          :label="group.label"
                          :value="group.value"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </el-collapse-item>
              </el-collapse>

              <!-- 其他可拖拽步骤 -->
              <el-collapse v-model="activeSteps">
                <draggable 
                  v-model="draggableSteps"
                  :animation="150"
                  handle=".drag-handle"
                  item-key="sort"
                  @start="dragStart"
                  @end="dragEnd"
                >
                  <template #item="{ element: step, index }">
                    <el-collapse-item 
                      :name="index + 1"
                      class="step-item"
                    >
                      <template #title>
                        <div class="step-header">
                          <div class="step-title">
                            <el-icon class="drag-handle">
                              <Rank />
                            </el-icon>
                            <div class="step-info">
                              <div class="step-dot" :style="{ backgroundColor: getStepColor(index + 1) }" />
                              <span>{{ step.name || $t('Approval.FlowCreate.step', { num: index + 2 }) }}</span>
                            </div>
                          </div>
                          <div class="step-actions">
                            <el-button 
                              link 
                              type="danger"
                              @click.stop="removeStep(index + 1)"
                            >
                              <el-icon><Delete /></el-icon>
                            </el-button>
                          </div>
                        </div>
                      </template>

                      <div class="step-content">
                        <el-form-item 
                          :label="$t('Approval.FlowCreate.stepName')" 
                          :prop="`steps.${index + 1}.name`"
                        >
                          <el-input 
                            v-model="step.name"
                            :placeholder="$t('Approval.FlowCreate.stepNamePlaceholder')"
                            style="width: 50%"
                          />
                        </el-form-item>

                        <el-form-item 
                          :label="$t('Approval.FlowCreate.stepCode')" 
                          :prop="`steps.${index + 1}.stepCode`"
                        >
                          <el-input 
                            v-model="step.stepCode"
                            :placeholder="$t('Approval.FlowCreate.stepCodePlaceholder')"
                            style="width: 50%"
                          />
                        </el-form-item>

                        <el-form-item 
                          :label="$t('Approval.FlowCreate.approvalRoles')" 
                          :prop="`steps.${index + 1}.groupIds`"
                        >
                          <el-select
                            v-model="step.groupIds"
                            multiple
                            collapse-tags
                            filterable
                            remote
                            :remote-method="remoteSearch"
                            :loading="groupLoading"
                            :placeholder="$t('Approval.FlowCreate.selectRoles')"
                            style="width: 50%"
                            clearable
                            @visible-change="handleVisibleChange"
                            @change="handleGroupIdsChange($event, index + 1)"
                          >
                            <el-option
                              v-for="group in groupOptions"
                              :key="group.value"
                              :label="group.label"
                              :value="group.value"
                            />
                          </el-select>
                        </el-form-item>
                      </div>
                    </el-collapse-item>
                  </template>
                </draggable>
              </el-collapse>
            </div>

            <!-- 流程设置 -->
            <div class="form-section">
              <h3>{{ $t('Approval.FlowCreate.settings.title') }}</h3>
              <div class="settings-list">
                <div class="setting-item">
                  <div class="setting-label">{{ $t('Approval.FlowCreate.settings.lockContent') }}</div>
                  <div class="setting-content">
                    <el-switch v-model="formData.settings.lock_published_content" />
                    <span class="setting-desc">{{ $t('Approval.FlowCreate.settings.lockContentDesc') }}</span>
                  </div>
                </div>

                <!--暂时屏蔽附件功能 <div class="setting-item">
                  <div class="setting-label">允許添加附件</div>
                  <div class="setting-content">
                    <el-switch v-model="formData.settings.allow_attachments" />
                    <span class="setting-desc">啟動工作流程時，允許引用媒體項目</span>
                  </div>
                </div> -->

                <div class="setting-item">
                  <div class="setting-label">{{ $t('Approval.FlowCreate.settings.allowPlannedDate') }}</div>
                  <div class="setting-content">
                    <el-switch v-model="formData.settings.allow_planned_date" />
                    <span class="setting-desc">{{ $t('Approval.FlowCreate.settings.allowPlannedDateDesc') }}</span>
                    <el-date-picker
                      v-if="formData.settings.allow_planned_date"
                      v-model="formData.settings.planned_date"
                      value-format="YYYY-MM-DD"
                      type="date"
                      :placeholder="$t('Approval.FlowCreate.settings.selectDate')"
                      style="width: 50%; margin-top: 8px;"
                    />
                  </div>
                </div>

                <div class="setting-item">
                  <div class="setting-label">{{ $t('Approval.FlowCreate.settings.enableTimeLimit') }}</div>
                  <div class="setting-content">
                    <el-switch v-model="formData.settings.enable_time_limit" />
                    <span class="setting-desc">{{ $t('Approval.FlowCreate.settings.enableTimeLimitDesc') }}</span>
                  </div>
                </div>

                <div class="setting-item">
                  <div class="setting-label">{{ $t('Approval.FlowCreate.settings.allowCancelPending') }}</div>
                  <div class="setting-content">
                    <el-switch v-model="formData.settings.allow_cancel_pending" />
                    <span class="setting-desc">{{ $t('Approval.FlowCreate.settings.allowCancelPendingDesc') }}</span>
                  </div>
                </div>

                <div class="setting-item">
                  <div class="setting-label">{{ $t('Approval.FlowCreate.settings.allowPermissionExtend') }}</div>
                  <div class="setting-content">
                    <el-switch v-model="formData.settings.allow_permission_extend" />
                    <span class="setting-desc">{{ $t('Approval.FlowCreate.settings.allowPermissionExtendDesc') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 通知設置 -->
            <div class="form-section notification-section">
              <h3>{{ $t('Approval.FlowCreate.notification.title') }}</h3>
              <div class="notification-rules">
                <div class="notification-group" v-for="(rule, index) in formData.settings.notification_rules" :key="index">
                  <div class="notification-row">
                    <span class="notification-label">{{ $t('Approval.FlowCreate.notification.send') }}</span>
                    <div class="notification-types">
                      <el-select
                        v-model="rule.channels"
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        class="notification-type-select"
                        :placeholder="$t('Approval.FlowCreate.notification.selectChannels')"
                      >
                        <el-option :label="$t('Approval.FlowCreate.notification.channels.email')" value="Email" />
                        <!-- <el-option label="系統通知" value="SystemNotification" />
                        <el-option label="SMS" value="SMS" /> -->
                      </el-select>
                    </div>
                    <span class="notification-label">{{ $t('Approval.FlowCreate.notification.when') }}</span>
                    <div class="notification-events">
                      <el-select
                        v-model="rule.eventType"
                        class="notification-event-select"
                        :placeholder="$t('Approval.FlowCreate.notification.selectEvent')"
                      >
                        <el-option 
                          :label="$t('Approval.FlowCreate.notification.events.processStart')" 
                          value="ProcessChange" 
                        />
                        <el-option 
                          :label="$t('Approval.FlowCreate.notification.events.processTimeout')" 
                          value="ProcessTimeout" 
                        />
                        <el-option 
                          :label="$t('Approval.FlowCreate.notification.events.emergencyTrigger')" 
                          value="EmergencyProcessTrigger" 
                        />
                      </el-select>
                    </div>
                    <el-button 
                      v-if="index !== 0" 
                      class="delete-rule" 
                      type="danger" 
                      link
                      @click="removeNotificationRule(index)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
                <el-button 
                  class="add-notification" 
                  type="primary" 
                  plain
                  :disabled="isMaxNotificationRules"
                  @click="addNotificationRule"
                >
                  <el-icon size="16"><Plus /></el-icon>
                  <span>{{ $t('Approval.FlowCreate.notification.addRule') }}</span>
                </el-button>
              </div>
            </div>

            
          </el-form>
        </div>
        <!-- 按钮组 -->
        <div class="flex justify-center" style="margin-top: 26px;">
          <el-button class="button-cancel" @click="handleCancel">{{ $t('Approval.FlowCreate.cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit">{{ $t('Approval.FlowCreate.confirm') }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  Plus,
  Rank,
  ArrowDown,
  ArrowUp,
  Delete,
  Loading
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { IApprovalFlow, IApprovalStep, IApprovalReviewGroup } from '../../types/index'
import { approvalService, reviewGroupService } from '../../services/approvalService'
import draggable from 'vuedraggable'

const router = useRouter()
const { t } = useI18n()
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<IApprovalFlow>({
  name: '',
  description: '',
  is_enabled: true,
  scope: '',
  steps: [
    {
      name: '',
      stepCode: '',
      sort: 1,
      groupIds: ''
    }
  ],
  settings: {
    lock_published_content: false,
    allow_attachments: false,
    allow_planned_date: false,
    planned_date: null,
    enable_time_limit: false,
    allow_cancel_pending: false,
    allow_permission_extend: false,
    notification_rules: []
  }
})

// 表单验证规则
const rules = computed<FormRules>(() => ({
  name: [
    { required: true, message: t('Approval.FlowCreate.rules.nameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Approval.FlowCreate.rules.nameLength'), trigger: 'blur' }
  ],
  scope: [
    { required: true, message: t('Approval.FlowCreate.rules.scopeRequired'), trigger: 'blur' },
    { max: 100, message: t('Approval.FlowCreate.rules.scopeLength'), trigger: 'blur' }
  ],
  description: [
    { max: 200, message: t('Approval.FlowCreate.rules.descriptionLength'), trigger: 'blur' }
  ],
  'steps.*.name': [
    { required: true, message: t('Approval.FlowCreate.rules.stepNameRequired'), trigger: 'blur' }
  ],
  'steps.*.stepCode': [
    { required: true, message: t('Approval.FlowCreate.rules.stepCodeRequired'), trigger: 'blur' }
  ],
  'steps.*.groupIds': [
    { required: true, message: t('Approval.FlowCreate.rules.groupRequired'), trigger: 'change' }
  ]
}))

// 添加审批组相关逻辑
const groupOptions = ref<Array<{label: string, value: string}>>([])
const groupLoading = ref(false)
const searchKeyword = ref('')
const isSelectOpen = ref(false)

// 处理下拉框可见性变化
const handleVisibleChange = (visible: boolean) => {
  if (visible) {
    // 下拉框打开时，如果已有数据就不重新请求
    isSelectOpen.value = true
    if (groupOptions.value.length === 0) {
      fetchGroupOptions()
    }
  } else {
    // 下拉框关闭时，清除搜索关键词并标记关闭状态
    isSelectOpen.value = false
    searchKeyword.value = ''
    // 当下拉框关闭时，延迟一会儿再重新请求全量数据
    // 这样避免与用户可能的选择操作冲突
    setTimeout(() => {
      if (!isSelectOpen.value) {
        fetchGroupOptions()
      }
    }, 200)
  }
}

// 远程搜索审批组
const remoteSearch = async (query: string) => {
  if (!isSelectOpen.value) return
  
  searchKeyword.value = query
  if (query.trim()) {
    await fetchGroupOptions()
  }
}

// 获取审批组列表的方法
const fetchGroupOptions = async () => {
  try {
    groupLoading.value = true
    const params: any = {
      page: 1,
      limit: 100,
    }
    
    // 添加搜索关键词
    if (searchKeyword.value && searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }
    
    const { data } = await reviewGroupService.getList(params)
    
    if (data.code === 200 && data.data) {
      groupOptions.value = data.data.items.map((item: { id: number; name: string }) => ({
        label: item.name,
        value: item.id.toString()
      }))
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowCreate.getGroupsFailed'))
  } finally {
    groupLoading.value = false
  }
}

// 当前展开的步骤
const activeSteps = ref<number[]>([0])

// 拖拽状态
const isDragging = ref(false)

// 步骤颜色
const stepColors = [
  '#F53F3F', // 红色 - 草稿
  '#0FC6C2', // 青色 - SEO
  '#14C9C9', // 蓝绿色 - 市场
  '#165DFF', // 蓝色 - 法务
  '#722ED1'  // 紫色 - 发布
]

// 获取步骤颜色
const getStepColor = (index: number) => {
  return stepColors[index % stepColors.length]
}

// 添加一个计算属性来处理可拖拽的步骤
const draggableSteps = computed({
  get: () => formData.steps.slice(1),
  set: (value) => {
    // 更新除第一步外的所有步骤
    formData.steps = [formData.steps[0], ...value]
    // 更新所有步骤的排序
    formData.steps.forEach((step, index) => {
      step.sort = index + 1
    })
  }
})

// 修改拖拽开始和结束的处理函数
const dragStart = () => {
  isDragging.value = true
}

const dragEnd = () => {
  isDragging.value = false
}

// 修改添加步骤的方法，确保正确设置 sort
const addStep = () => {
  const newStep: IApprovalStep = {
    name: `${t('Approval.FlowCreate.step', { num: formData.steps.length + 1 })}`,
    stepCode: '',
    sort: formData.steps.length + 1,
    groupIds: ''
  }
  formData.steps.push(newStep)
}

// 修改删除步骤的方法，确保正确更新 sort
const removeStep = (index: number) => {
  formData.steps.splice(index, 1)
  // 更新剩余步骤的排序
  formData.steps.forEach((step, i) => {
    step.sort = i + 1
  })
}

// 添加一个计算属性来控制添加按钮的禁用状态
const isMaxNotificationRules = computed(() => {
  return formData.settings.notification_rules.length >= 3
})

// 修改初始化通知规则的方法
const initNotificationRules = () => {
  formData.settings.notification_rules = [{
    eventType: 'ProcessChange',
    channels: ['Email']
  }]
}

// 修改添加通知规则的方法
const addNotificationRule = () => {
  if (formData.settings.notification_rules.length >= 3) {
    ElMessage.warning(t('Approval.FlowCreate.maxNotificationRules'))
    return
  }
  
  formData.settings.notification_rules.push({
    eventType: 'ProcessChange',
    channels: ['Email']
  })
}

// 删除通知规则
const removeNotificationRule = (index: number) => {
  formData.settings.notification_rules.splice(index, 1)
}

// 在组件挂载时初始化通知规则
onMounted(() => {
  if (!formData.settings.notification_rules?.length) {
    initNotificationRules()
  }
  fetchGroupOptions()
})

// 取消
const handleCancel = () => {
  router.back()
}

// 处理审批组选择变化
const handleGroupIdsChange = (values: string[], index: number) => {
  // formData.steps[index].groupIds = values.join(',')
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    await approvalService.create(formData)
    ElMessage.success(t('Approval.FlowCreate.createSuccess'))
    router.push('/approval/flows')
  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error(t('Approval.FlowCreate.createFailed'))
  }
}
</script>

<style lang="scss" scoped>
.bwms-module {
  
  .module-con {
    
    .box {
      padding-top: 20px;
      .form-section {
        margin-bottom: 32px;
        
        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;

          h3 {
            margin: 0;
          }
        }

        h3 {
          font-size: 16px;
          font-weight: 500;
          color: #000;
          margin-bottom: 24px;
        }

        .step-item {
          margin-bottom: 16px;
          padding: 16px;
          border: 1px solid #E5E6EB;
          border-radius: 4px;

          .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .step-title {
              display: flex;
              align-items: center;
              gap: 8px;
              font-weight: 500;
              
              .drag-handle {
                cursor: move;
                color: #86909C;
              }
            }

            .step-actions {
              display: flex;
              gap: 8px;
              margin-top: 5px;
              margin-left: 12px;
            }
          }

          .step-content {
            padding-top: 16px;
          }
        }

        .setting-desc {
          margin-left: 12px;
          color: #86909C;
          font-size: 16px;
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 40px;
      }
    }
  }
}

.step-item {
  margin-bottom: 16px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  overflow: hidden;

  &.is-first {
    .drag-handle {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  .step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;

    .step-title {
      display: flex;
      align-items: center;
      gap: 12px;

      .drag-handle {
        cursor: move;
        color: #86909C;
        font-size: 16px;

        &.not-draggable {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }

      .step-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .step-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }

        span {
          font-size: 16px;
          font-weight: 500;
          color: #000;
        }
      }
    }
  }

  .step-content {
    padding: 16px;
    background-color: #F7F8FA;
  }
}

// 拖拽时的样式
.sortable-ghost {
  opacity: 0.5;
  background: #F2F3F5;
}

.sortable-chosen {
  background: #FFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.first-step {
  margin-bottom: 24px;
  border: 1px solid #E5E6EB;
  border-radius: 4px;
  
  .step-header {
    background-color: #F7F8FA;
  }
}

.settings-list {
  .setting-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .setting-label {
      min-width: 200px;
      padding-right: 12px;
      color: #000;
      font-size: 16px;
      line-height: 32px;
    }

    .setting-content {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;

      .setting-desc {
        color: #86909C;
        font-size: 16px;
      }

      .el-date-picker {
        margin-left: 0;
        width: 100%;
      }
    }
  }
}

.notification-section {
  .notification-rules {
    padding: 16px;
    background-color: #F7F8FA;
    border-radius: 4px;

    .notification-group {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 24px;
      }
    }

    .notification-row {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 4px;
      margin-bottom: 12px;

      .notification-label {
        font-size: 16px;
        color: #000;
        min-width: 32px;
      }

      .notification-type-select {
        width: 260px;
      }

      .notification-event-select {
        width: 200px;
      }

      .delete-rule {
        margin-left: auto;
      }
    }

    .add-notification {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      font-size: 14px;
    }
  }
}

:deep(.el-select) {
  .el-select__tags {
    .el-tag {
      background-color: #E8F3FF;
      border-color: #E8F3FF;
      color: #165DFF;
      
      .el-tag__close {
        color: #165DFF;
        
        &:hover {
          background-color: #165DFF;
          color: #fff;
        }
      }
    }
  }
}

// 日期选择器的上边距
.el-date-picker {
  margin-left: 120px; // 对齐输入框
}
</style> 
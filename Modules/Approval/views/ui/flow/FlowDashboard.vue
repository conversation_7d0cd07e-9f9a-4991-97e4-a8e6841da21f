<template>
  <div class="bwms-module table-page">
    <div class="module-header"></div>
    <!-- 内容区域 -->
    <div class="module-con">
      <!-- 流程状态统计 -->
      <div class="box status-box">
        <div class="status-title">{{ $t('Approval.FlowDashboard.flowStatus') }}</div>
        <div class="status-list">
          <div class="status-item">
            <div class="status-name">{{ $t('Approval.FlowDashboard.processing') }}</div>
            <div class="status-value">{{ stats.processing_count || 0 }}</div>
          </div>
          <div class="status-item">
            <div class="status-name">{{ $t('Approval.FlowDashboard.timeout') }}</div>
            <div class="status-value">{{ stats.timeout_count || 0 }}</div>
          </div>
          <div class="status-item">
            <div class="status-name">{{ $t('Approval.FlowDashboard.pendingPublish') }}</div>
            <div class="status-value">{{ stats.pending_publish_count || 0 }}</div>
          </div>
        </div>
      </div>

      <!-- 流程列表 -->
      <div class="box">
        <!-- 标签页切换 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane :label="$t('Approval.FlowDashboard.pendingApproval')" name="pending">
            <!-- 搜索区域 -->
            <div class="search-area">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="$t('Approval.FlowDashboard.keyword')"
                class="search-input"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <div class="filter-btn">
                <el-button @click="handleFilter">
                  <el-icon><Filter /></el-icon>
                  {{ $t('Approval.FlowDashboard.filter') }}
                </el-button>
              </div>
            </div>

            <!-- 表格区域 -->
            <el-table
              v-loading="loading.pending"
              :data="pendingData"
              style="width: 100%"
            >
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template> 
              <el-table-column prop="product_name" :label="$t('Approval.FlowDashboard.projectName')" min-width="150">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleApproval(row)"
                  >
                    {{ row.product_name }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="product_category" :label="$t('Approval.FlowDashboard.contentType')" width="150" />
              <el-table-column :label="$t('Approval.FlowDashboard.deadline')" width="220">
                <template #default="{ row }">
                  {{ formatExpireTime(row.expire_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="updated_at" :label="$t('Approval.FlowDashboard.lastModified')" width="220" />
              <el-table-column :label="$t('Approval.FlowDashboard.operations')" width="160" fixed="right">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleApproval(row)"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleDelete(row)"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane :label="$t('Approval.FlowDashboard.publishedApproval')" name="published">
            <!-- 搜索区域 -->
            <div class="search-area">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="$t('Approval.FlowDashboard.keyword')"
                class="search-input"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <div class="filter-btn">
                <el-button @click="handleFilter">
                  <el-icon><Filter /></el-icon>
                  {{ $t('Approval.FlowDashboard.filter') }}
                </el-button>
              </div>
            </div>

            <!-- 表格区域 -->
            <el-table
              v-loading="loading.published"
              :data="publishedData"
              style="width: 100%"
            >
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template> 
              <el-table-column prop="product_name" :label="$t('Approval.FlowDashboard.projectName')" min-width="150">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleApproval(row)"
                  >
                    {{ row.product_name }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="product_category" :label="$t('Approval.FlowDashboard.contentType')" width="150" />
              <el-table-column :label="$t('Approval.FlowDashboard.deadline')" width="220">
                <template #default="{ row }">
                  {{ formatExpireTime(row.expire_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="updated_at" :label="$t('Approval.FlowDashboard.lastModified')" width="220" />
              <el-table-column :label="$t('Approval.FlowDashboard.operations')" width="160" fixed="right">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleApproval(row)"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleDelete(row)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane :label="$t('Approval.FlowDashboard.completedApproval')" name="completed">
            <!-- 搜索区域 -->
            <div class="search-area">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="$t('Approval.FlowDashboard.keyword')"
                class="search-input"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <div class="filter-btn">
                <el-button @click="handleFilter">
                  <el-icon><Filter /></el-icon>
                  {{ $t('Approval.FlowDashboard.filter') }}
                </el-button>
              </div>
            </div>

            <!-- 表格区域 -->
            <el-table
              v-loading="loading.completed"
              :data="completedData"
              style="width: 100%"
            >
              <template #empty>
                <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
              </template> 
              <el-table-column prop="product_name" :label="$t('Approval.FlowDashboard.projectName')" min-width="150">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleApproval(row)"
                  >
                    {{ row.product_name }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="product_category" :label="$t('Approval.FlowDashboard.contentType')" width="150" />
              <el-table-column :label="$t('Approval.FlowDashboard.deadline')" width="220">
                <template #default="{ row }">
                  {{ formatExpireTime(row.expire_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="updated_at" :label="$t('Approval.FlowDashboard.lastModified')" width="220" />
              <el-table-column :label="$t('Approval.FlowDashboard.operations')" width="160" fixed="right">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleApproval(row)"
                  >
                    <el-icon><View /></el-icon>
                  </el-button>
                  <el-button 
                    link 
                    type="primary" 
                    @click="handleDelete(row)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style" v-if="activeTab === 'pending'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pending.pageSize"
              class="page-size-select"
              @change="val => handleSizeChange(val, 'pending')"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.pending.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.pending.currentPage"
              background
              layout="prev, pager, next"
              :page-size="pagination.pending.pageSize"
              :total="pagination.pending.total"
              @current-change="val => handleCurrentChange(val, 'pending')"
            />
          </div>
        </div>
        <div class="pagination table-pagination-style" v-if="activeTab === 'published'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.published.pageSize"
              class="page-size-select"
              @change="val => handleSizeChange(val, 'published')"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.published.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.published.currentPage"
              background
              layout="prev, pager, next"
              :page-size="pagination.published.pageSize"
              :total="pagination.published.total"
              @current-change="val => handleCurrentChange(val, 'published')"
            />
          </div>
        </div>
        <div class="pagination table-pagination-style" v-if="activeTab === 'completed'">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.completed.pageSize"
              class="page-size-select"
              @change="val => handleSizeChange(val, 'completed')"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.completed.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.completed.currentPage"
              background
              layout="prev, pager, next"
              :page-size="pagination.completed.pageSize"
              :total="pagination.completed.total"
              @current-change="val => handleCurrentChange(val, 'completed')"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  Search,
  Filter,
  More,
  View,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { approvalService } from '../../services/approvalService'
import { de } from 'element-plus/es/locale'

const router = useRouter()
const { t } = useI18n()

// 当前激活的标签页
const activeTab = ref('pending')

// 加载状态 - 分别设置每个标签页的加载状态
const loading = reactive({
  pending: false,
  published: false,
  completed: false
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: '',
  status: '',
  dateRange: []
})

// 表格数据 - 为每个标签页设置单独的数据源
const pendingData = ref([])
const publishedData = ref([])
const completedData = ref([])

// 统计数据
const stats = reactive({
  processing_count: 0,
  timeout_count: 0,
  pending_publish_count: 0
})

// 分页数据 - 为每个标签页设置单独的分页
const pagination = reactive({
  pending: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  published: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  completed: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  }
})

// 根据状态获取标签类型
const getStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'info',    // 草稿
    1: 'warning', // 审批中
    2: 'success', // 已发布
    3: 'danger',  // 已拒绝
    4: 'info'     // 已完成
  }
  return typeMap[status] || 'info'
}

// 标签页切换
const handleTabClick = () => {
  // 重置当前标签页的分页和搜索条件
  const tabName = activeTab.value as 'pending' | 'published' | 'completed'
  
  // 重置搜索关键词
  searchForm.keyword = ''
  
  // 重置分页
  pagination[tabName].currentPage = 1
  pagination[tabName].pageSize = 10
  
  // 清空数据
  if (tabName === 'pending') {
    pendingData.value = []
  } else if (tabName === 'published') {
    publishedData.value = []
  } else if (tabName === 'completed') {
    completedData.value = []
  }
  
  // 重新获取数据
  fetchData()
}

// 获取数据
const fetchData = async () => {
  const tabName = activeTab.value as 'pending' | 'published' | 'completed'
  loading[tabName] = true
  
  try {
    // 根据当前标签页决定获取什么数据
    const params = {
      page: pagination[tabName].currentPage,
      limit: pagination[tabName].pageSize,
      status: tabName, // 传递标签页类型作为参数
      keyword: searchForm.keyword
    }
    
    const { data } = await approvalService.getRecordsList(params)
    
    // 更新统计数据
    if (data.data) {
      stats.processing_count = data.data.processing_count || 0
      stats.timeout_count = data.data.timeout_count || 0
      stats.pending_publish_count = data.data.pending_publish_count || 0
    }
    
    // 获取列表数据
    const items = data.data?.items || []
    const total = data.data?.total || items.length
    
    // 根据当前标签页更新对应的数据源
    if (tabName === 'pending') {
      pendingData.value = items
      pagination.pending.total = total
    } else if (tabName === 'published') {
      publishedData.value = items
      pagination.published.total = total
    } else if (tabName === 'completed') {
      completedData.value = items
      pagination.completed.total = total
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowDashboard.getDataFailed'))
  } finally {
    loading[tabName] = false
  }
}

// 分页大小变化
const handleSizeChange = (val: number, type: 'pending' | 'published' | 'completed') => {
  pagination[type].pageSize = val
  if (activeTab.value === type) {
    fetchData()
  }
}

// 页码变化
const handleCurrentChange = (val: number, type: 'pending' | 'published' | 'completed') => {
  pagination[type].currentPage = val
  if (activeTab.value === type) {
    fetchData()
  }
}

// 筛选
const handleFilter = () => {
  // 重置当前标签页的页码
  const tabName = activeTab.value as 'pending' | 'published' | 'completed'
  pagination[tabName].currentPage = 1
  fetchData()
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    t('Approval.FlowDashboard.deleteConfirmContent'),
    t('Approval.FlowList.tip'),
    {
      
      confirmButtonText: t('Approval.FlowList.confirm'),
      cancelButtonText: t('Approval.FlowList.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      await approvalService.deleteRecord(row.id)
      ElMessage.success(t('Approval.FlowList.deleteSuccess'))
      fetchData()
    } catch (error) {
      ElMessage.error(t('Approval.FlowList.deleteFailed'))
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 编辑
const handleEdit = (row: any) => {
  router.push(`/approval/flows/${row.id}/edit`)
}

// 归档
const handleArchive = (row: any) => {
  // 实现归档逻辑
  ElMessage.success('归档功能开发中')
}

// 审批
const handleApproval = (row: any) => {
  router.push(`/approval/records/${row.id}/approval`)
}

// 判断是否可以审批
const canApprove = (row: any) => {
  // 实际逻辑：只有状态为审批中(1)的项目才能进行审批
  return row.status === 1
}

// 格式化截止日期
const formatExpireTime = (expireTime: string | number | null | undefined) => {
  if (!expireTime || expireTime === 0) {
    return t('Approval.FlowDashboard.noDeadline')
  }
  return expireTime
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      overflow: auto;
      
      &.status-box {
        margin-bottom: 16px;
        &::-webkit-scrollbar {
          width: 5px;
          height: 5px;
        }

        &::-webkit-scrollbar-button {
          display: none;
        }

        &::-webkit-scrollbar-corner {
          background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgb(0, 126, 229, 0.3);
          border-radius: 5px;
          background-clip: content-box;
        }
        .status-title {
          font-size: 16px;
          font-weight: 500;
          color: #1D2129;
          margin-bottom: 10px;
        }
        padding: 10px 15px; /* 减小内边距 */
        margin-bottom: 10px; /* 减小底部间距 */
        flex-grow: 0;
        /* 移除了标题，直接显示统计项 */
        .status-list {
          display: flex;
          gap: 12px; /* 减小列间距 */
          min-height: 60px; /* 设置最小高度 */

          .status-item {
            flex: 1;
            padding: 8px 10px; /* 减小内边距 */
            background: #F7F8FA;
            border-radius: 4px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .status-name {
              font-size: 12px; /* 减小字体 */
              color: #4E5969;
              margin-bottom: 4px; /* 减小间距 */
            }

            .status-value {
              font-size: 18px; /* 减小字体 */
              font-weight: 500;
              color: #1D2129;
              line-height: 1.2; /* 减小行高 */
            }
          }
        }
      }

      .search-area {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        gap: 16px;

        .search-input {
          width: 300px;
        }

        .filter-btn {
          margin-right: auto;
        }
      }

      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: #E5E6EB;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  
  &.is-active {
    font-weight: 500;
  }
}

.owner-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .el-avatar {
    flex-shrink: 0;
  }
  
  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style> 
<template>
  <div class="table-page bwms-module">
    <!-- 头部区域 -->
    <div class="module-header">
      <div class="btn-list">
        <el-button type="primary" @click="handleEdit">
          <el-icon size="16"><Edit /></el-icon>
          <span>{{ $t('Approval.FlowDetail.edit') }}</span>
        </el-button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box scroll-bar-custom" v-loading="loading">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>{{ $t('Approval.FlowDetail.basicInfo') }}</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item :label="$t('Approval.FlowDetail.name')">
              {{ flowData.name }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.scope')">
              {{ flowData.scope }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.description')">
              {{ flowData.description }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.createdAt')">
              {{ formatDateTime(flowData.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.updatedAt')">
              {{ formatDateTime(flowData.updatedAt) }}
            </el-descriptions-item>
            <!-- <el-descriptions-item label="状态">
              <el-tag :type="flowData.isEnabled === 1 ? 'success' : 'info'">
                {{ flowData.isEnabled === 1 ? '已启用' : '已禁用' }}
              </el-tag>
            </el-descriptions-item> -->
          </el-descriptions>
        </div>

        <!-- 步骤信息 -->
        <div class="detail-section">
          <h3>{{ $t('Approval.FlowDetail.stepInfo') }}</h3>
          <el-steps :active="flowData.steps.length" direction="vertical">
            <el-step 
              v-for="(step, index) in flowData.steps" 
              :key="index"
              :icon="getStepIcon(index)"
              :status="getStepStatus(index)"
            >
              <template #title>
                <span class="step-name">{{ step.name }}</span>
              </template>
              <template #description>
                <div class="step-info">
                  <p class="step-code">{{ $t('Approval.FlowDetail.stepCode') }}: {{ step.stepCode }}</p>
                  <p v-if="step.groups && step.groups.length">
                    {{ $t('Approval.FlowDetail.approvalRoles') }}: 
                    <el-tag 
                      v-for="group in step.groups" 
                      :key="group.id"
                      size="small"
                      class="role-tag"
                    >
                      {{ group.name }}
                    </el-tag>
                  </p>
                  <!-- <p v-if="step.rejectToDraft === 1">
                    <el-tag size="small" type="info">允许退回草稿</el-tag>
                  </p> -->
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>

        <!-- 流程设置 -->
        <div class="detail-section">
          <h3>{{ $t('Approval.FlowDetail.settings.title') }}</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item :label="$t('Approval.FlowDetail.settings.lockContent')">
              {{ flowData.settings.lock_published_content === 1 ? $t('Approval.FlowDetail.yes') : $t('Approval.FlowDetail.no') }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.settings.allowAttachments')">
              {{ flowData.settings.allow_attachments === 1 ? $t('Approval.FlowDetail.yes') : $t('Approval.FlowDetail.no') }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.settings.allowPlannedDate')">
              {{ flowData.settings.allow_planned_date === 1 ? $t('Approval.FlowDetail.yes') : $t('Approval.FlowDetail.no') }}
            </el-descriptions-item>
            <el-descriptions-item v-if="flowData.settings.allow_planned_date === 1" :label="$t('Approval.FlowDetail.settings.plannedDate')">
              {{ flowData.settings.planned_date }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.settings.enableTimeLimit')">
              {{ flowData.settings.enable_time_limit === 1 ? $t('Approval.FlowDetail.yes') : $t('Approval.FlowDetail.no') }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.settings.allowCancelPending')">
              {{ flowData.settings.allow_cancel_pending === 1 ? $t('Approval.FlowDetail.yes') : $t('Approval.FlowDetail.no') }}
            </el-descriptions-item>
            <el-descriptions-item :label="$t('Approval.FlowDetail.settings.allowPermissionExtend')">
              {{ flowData.settings.allow_permission_extend === 1 ? $t('Approval.FlowDetail.yes') : $t('Approval.FlowDetail.no') }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 通知规则 -->
        <div class="detail-section">
          <h3>{{ $t('Approval.FlowDetail.notification.title') }}</h3>
          <el-descriptions :column="1" border v-if="flowData.settings.notification_rules && flowData.settings.notification_rules.length">
            <el-descriptions-item 
              v-for="(rule, index) in flowData.settings.notification_rules"
              :key="index"
              :label="getNotificationLabel(rule)"
            >
              <el-tag 
                v-for="channel in rule.channels" 
                :key="channel"
                size="small"
                class="channel-tag"
              >
                {{ getChannelName(channel) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          <el-empty v-else :description="$t('Approval.FlowDetail.notification.empty')" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { IApprovalFlow } from '../../types/index' 
import { approvalService } from '../../services/approvalService'

const { t } = useI18n()

interface GroupInfo {
  id: number
  name: string
}

interface Step {
  name: string
  stepCode: string
  groups: GroupInfo[]
  rejectToDraft?: number
}

interface FlowDataType {
  id: number
  name: string
  description: string
  scope: string
  settings: {
    lock_published_content: number
    allow_attachments: number
    allow_planned_date: number
    planned_date: string | null
    enable_time_limit: number
    allow_cancel_pending: number
    allow_permission_extend: number
    notification_rules: any[]
  }
  isEnabled: number
  createdAt: number
  updatedAt: number
  steps: Step[]
}

const router = useRouter()
const route = useRoute()
const loading = ref(false)

// 流程数据，使用接口返回的数据结构
const flowData = ref<FlowDataType>({
  id: 0,
  name: '',
  description: '',
  scope: '',
  settings: {
    lock_published_content: 0,
    allow_attachments: 0,
    allow_planned_date: 0,
    planned_date: null,
    enable_time_limit: 0,
    allow_cancel_pending: 0,
    allow_permission_extend: 0,
    notification_rules: []
  },
  isEnabled: 1,
  createdAt: 0,
  updatedAt: 0,
  steps: []
})

// 获取步骤图标
const getStepIcon = (index: number) => {
  if (index === 0) return 'Edit'
  if (index === flowData.value.steps.length - 1) return 'Select'
  return 'Document'
}

// 获取步骤状态
const getStepStatus = (index: number) => {
  return 'success'
}

// 格式化时间戳
const formatDateTime = (timestamp: number) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-')
}

// 获取通知规则标签
const getNotificationLabel = (rule: { eventType: string; channels: string[] }) => {
  const eventMap: {[key: string]: string} = {
    'ProcessChange': t('Approval.FlowDetail.notification.events.processStart'),
    'ProcessTimeout': t('Approval.FlowDetail.notification.events.processTimeout'),
    'EmergencyProcessTrigger': t('Approval.FlowDetail.notification.events.emergencyTrigger')
  }
  
  return eventMap[rule.eventType] || rule.eventType
}

// 获取通知渠道名称
const getChannelName = (channel: string) => {
  const channelMap: {[key: string]: string} = {
    'Email': t('Approval.FlowDetail.notification.channels.email'),
    'SMS': t('Approval.FlowDetail.notification.channels.sms'),
    'SystemNotification': t('Approval.FlowDetail.notification.channels.system')
  }
  
  return channelMap[channel] || channel
}

// 编辑流程
const handleEdit = () => {
  router.push(`/approval/flows/${route.params.id}/edit`)
}

// 获取流程数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await approvalService.getDetail(Number(route.params.id))
    
    if (data.code === 200 && data.data.item) {
      flowData.value = data.data.item
    }
  } catch (error) {
    ElMessage.error(t('Approval.FlowDetail.getDetailFailed'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;
      .detail-section {
        margin-bottom: 32px;

        h3 {
          font-size: 16px;
          font-weight: 500;
          color: #1D2129;
          margin-bottom: 24px;
        }

        .step-info {
          padding: 12px;
          background: #F7F8FA;
          border-radius: 4px;

          p {
            margin: 8px 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            color: #4E5969;

            &.step-code {
              color: #86909C;
              font-size: 13px;
            }
          }

          .role-tag,
          .step-tag {
            margin-right: 4px;
            margin-bottom: 4px;
          }
        }

        .step-name {
          font-weight: 500;
          color: #1D2129;
        }
        
        .channel-tag {
          margin-right: 8px;
        }
      }
    }
  }
}
</style> 
<template>
  <div class="table-page bwms-module">

    <div class="module-header">
    </div>
    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box scroll-bar-custom">
        <el-form
          v-loading="loading"
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
          class="review-group-form"
        >
          <el-form-item :label="$t('Approval.ReviewGroup.name')" prop="name">
            <el-input
              v-model="formData.name"
              :placeholder="$t('Approval.ReviewGroup.namePlaceholder')"
              style="width: 50%"
            />
          </el-form-item>

          <el-form-item :label="$t('Approval.ReviewGroup.memberList')" prop="members">
            <div class="member-list">
              <div 
                v-for="(member, index) in formData.members"
                :key="index"
                class="member-item"
              >
                <el-form-item
                  :prop="`members.${index}.name`"
                  :rules="[
                    { required: true, message: t('Approval.ReviewGroup.rules.memberNameRequired'), trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model="member.name"
                    :placeholder="$t('Approval.ReviewGroup.memberNamePlaceholder')"
                  />
                </el-form-item>
                <el-form-item
                  :prop="`members.${index}.position`"
                  :rules="[
                    { required: true, message: t('Approval.ReviewGroup.rules.positionRequired'), trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model="member.position"
                    :placeholder="$t('Approval.ReviewGroup.positionPlaceholder')"
                  />
                </el-form-item>
                <el-form-item
                  :prop="`members.${index}.email`"
                  :rules="[
                    { required: true, message: t('Approval.ReviewGroup.rules.emailRequired'), trigger: 'blur' },
                    { type: 'email', message: t('Approval.ReviewGroup.rules.emailInvalid'), trigger: 'blur' }
                  ]"
                >
                  <el-input
                    v-model="member.email"
                    :placeholder="$t('Approval.ReviewGroup.emailPlaceholder')"
                  />
                </el-form-item>
                <div class="member-actions">
                  <el-switch
                    v-model="member.isEnabled"
                    :active-text="$t('Approval.ReviewGroup.enabled')"
                    :inactive-text="$t('Approval.ReviewGroup.disabled')"
                  />
                  <el-button 
                    style="margin-top: 5px;"
                    type="danger" 
                    link
                    @click="removeMember(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="add-member">
                <el-button type="primary" link @click="addMember">
                  <el-icon><Plus /></el-icon>
                  {{ $t('Approval.ReviewGroup.addMember') }}
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 按钮组 -->
    <div class="flex justify-center" style="margin-top: 26px;">
      <el-button class="button-cancel" @click="handleCancel">{{ $t('Approval.ReviewGroup.cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit">{{ $t('Approval.ReviewGroup.save') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { IApprovalReviewGroup } from '../../types/index'
import { reviewGroupService } from '../../services/approvalService'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据类型
interface IReviewGroupForm {
  id: number
  name: string
  members: Array<{
    name: string
    position: string
    email: string
    isEnabled: boolean
  }>
}

// 表单数据
const formData = reactive<IReviewGroupForm>({
  id: 0,
  name: '',
  members: []
})

// 表单验证规则
const rules = computed<FormRules>(() => ({
  name: [
    { required: true, message: t('Approval.ReviewGroup.rules.nameRequired'), trigger: 'blur' },
    { min: 2, max: 50, message: t('Approval.ReviewGroup.rules.nameLength'), trigger: 'blur' }
  ],
  members: [
    { required: true, message: t('Approval.ReviewGroup.rules.membersRequired'), trigger: 'change' }
  ]
}))

// 添加成员
const addMember = () => {
  formData.members.push({
    name: '',
    position: '',
    email: '',
    isEnabled: true
  })
}

// 移除成员
const removeMember = (index: number) => {
  formData.members.splice(index, 1)
}

// 取消
const handleCancel = () => {
  router.back()
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    const submitData = {
      name: formData.name,
      members: formData.members.map(member => ({
        name: member.name,
        position: member.position,
        email: member.email,
        is_enabled: member.isEnabled ? 1 : 0
      }))
    }
    await reviewGroupService.update(formData.id, submitData)
    ElMessage.success(t('Approval.ReviewGroup.updateSuccess'))
    router.push('/approval/groups')
  } catch (error) {
    ElMessage.error(t('Approval.ReviewGroup.updateFailed'))
  }
}

// 获取审核组数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await reviewGroupService.getDetail(Number(route.params.id))
    
    // 根据新的数据格式修改数据转换
    if (data.code === 200 && data.data.item) {
      const item = data.data.item
      formData.id = item.id
      formData.name = item.name
      formData.members = item.members.map((member: {
        id: number
        name: string
        position: string
        email: string
        is_enabled: number
      }) => ({
        name: member.name,
        position: member.position,
        email: member.email,
        isEnabled: member.is_enabled === 1
      }))
    }
  } catch (error) {
    ElMessage.error(t('Approval.ReviewGroup.getDetailFailed'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.bwms-module {
  .module-con {
    .box {
      padding-top: 20px;

      .review-group-form {
        :deep(.el-form-item__content) {
          width: 100%;
        }
      }

      .member-list {
        width: 100%;
        
        .member-item {
          display: grid;
          grid-template-columns: 2fr 2fr 2fr 1fr;
          gap: 16px;
          align-items: start;
          margin-bottom: 16px;
          padding: 16px 12px;
          background: #F7F8FA;
          border-radius: 10px;
          transition: all 0.3s ease;

          &:hover {
            background: #F2F3F5;
          }

          &:last-child {
            margin-bottom: 8px;
          }

          :deep(.el-form-item) {
            margin-bottom: 0;
          }

          :deep(.el-input) {
            width: 100%;
          }

          .member-actions {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding-top: 4px;
            width: 146px;
          }
        }

        .add-member {
          margin-top: 16px;
        }
      }
    }
  }
}
</style> 
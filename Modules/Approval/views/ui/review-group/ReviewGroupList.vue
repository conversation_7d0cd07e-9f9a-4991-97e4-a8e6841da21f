<template>
  <div class="bwms-module table-page">
  

    <!-- 头部区域 -->
    <div class="module-header">
        <!-- 筛选 -->
        <FilterPopover v-model="showFilterDropdown">
          <template #reference>
            <el-button class="button-no-border filter-trigger" @click="showFilterDropdown = !showFilterDropdown">
              <el-icon>
                <img src="/resources/admin/assets/icon/FilterIcon.png" alt="FilterIcon" />
              </el-icon>
              <span>{{ $t('Cms.list.filter') }}</span>
            </el-button>
          </template>
          <el-form :inline="true" :model="searchForm" label-position="top" style="width: 100%;">
            <el-form-item :label="$t('Approval.ReviewGroup.namePlaceholder')" style="width: 100%;">
              <el-input 
                v-model="searchForm.keyword"
                :placeholder="$t('Approval.ReviewGroup.rules.nameRequired')"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="flex justify-center">
              <el-button class="el-button-default" @click="handleReset">
                <el-icon><Refresh /></el-icon>
                <span>{{ $t('Cms.list.refresh') }}</span>
              </el-button>
              <el-button class="button-no-border" type="primary" @click="handleSearch">
                <el-icon><Filter /></el-icon>
                <span>{{ $t('Cms.list.filter') }}</span>
              </el-button>
            </div>
          </template>
        </FilterPopover>

        <el-button type="primary" @click="handleCreate">
          <el-icon><img src="/resources/admin/assets/icon/PlusIcon.png" alt="PlusIcon" /></el-icon>
          <span>{{ $t('Approval.ReviewGroup.create') }}</span>
        </el-button>
    </div>

    <!-- 内容区域 -->
    <div class="module-con">
      <div class="box">
        

        <!-- 表格 -->
        <el-table 
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
        >
          <template #empty>
            <el-empty :description="$t('Cms.list.no_data')" image-size="100px" />
          </template>
          <el-table-column prop="name" :label="$t('Approval.ReviewGroup.name')" min-width="120" />
          <el-table-column :label="$t('Approval.ReviewGroup.memberCount')" min-width="120">
            <template #default="{ row }">
              <span>{{ row.members_count || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('Approval.ReviewGroup.operations')" width="200" fixed="right">
            <template #default="{ row }">
              <div class="bwms-operate-btn-box">
                <div class="bwms-operate-btn" 
                  @click="handleEdit(row)"
                >
                  <el-icon size="16"><img src="/resources/admin/assets/icon/EditIcon.png" alt="EditIcon" /></el-icon>
                </div>
                <div class="bwms-operate-btn" 
                  @click="handleCopy(row)"
                >
                  <el-icon size="16"><img src="/resources/admin/assets/icon/CopyDocumentIcon.png" alt="CopyDocumentIcon" /></el-icon>
                </div>
                <div class="bwms-operate-btn" 
                  @click="handleDelete(row)"
                >
                  <el-icon size="16"><img src="/resources/admin/assets/icon/DeleteIcon.png" alt="DeleteIcon" /></el-icon>
                </div>
              </div>
              
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box-footer">
        <!-- 分页 -->
        <div class="pagination table-pagination-style">
          <div class="pagination-left">
            <span class="page-size-text">{{ $t('Cms.pagination.page_size_text') }}</span>
            <el-select
              v-model="pagination.pageSize"
              class="page-size-select"
              @change="handleSizeChange"
              size="default"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="size"
                :value="size"
                class="page-size-option"
              />
            </el-select>
            <span class="total-text">{{ $t('Cms.pagination.total_items', { total: pagination.total }) }}</span>
          </div>
          <div class="pagination-right">
            <el-pagination
              v-model:current-page="pagination.page"
              background
              layout="prev, pager, next"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Plus, Edit, CopyDocument, Delete, Search, Filter, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { IPageParams, ISearchForm, IApprovalReviewGroup } from '../../types/index'
import { reviewGroupService } from '../../services/approvalService'

const router = useRouter()
const { t } = useI18n()
const loading = ref(false)
const deleteRow = ref<IApprovalReviewGroup | null>(null)

// 修改分页参数
const searchParams = reactive<IPageParams>({
  page: 1,
  limit: 10,
  // sort_field: 'created_at',
  // sort_order: 'desc',
  keyword: ''
})

// 搜索表单
const searchForm = reactive<ISearchForm>({
  keyword: ''
})

// 表格数据
const tableData = ref<IApprovalReviewGroup[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 添加筛选下拉框状态
const showFilterDropdown = ref(false)

// 修改搜索处理函数
const handleSearch = () => {
  searchParams.page = 1
  searchParams.keyword = searchForm.keyword
  showFilterDropdown.value = false
  fetchData()
}

// 修改重置处理函数
const handleReset = () => {
  searchForm.keyword = ''
  searchParams.keyword = ''
  searchParams.page = 1
  showFilterDropdown.value = false
  fetchData()
}

// 改变每页条数
const handleSizeChange = (val: number) => {
  searchParams.limit = val
  searchParams.page = 1
  fetchData()
}

// 改变页码
const handleCurrentChange = (val: number) => {
  searchParams.page = val
  fetchData()
}

// 创建审核组
const handleCreate = () => {
  router.push('/approval/groups/create')
}

// 编辑审核组
const handleEdit = (row: IApprovalReviewGroup) => {
  router.push(`/approval/groups/${row.id}/edit`)
}

// 删除审核组
const handleDelete = (row: IApprovalReviewGroup) => {
  if (!row.id) return
  deleteRow.value = row
  ElMessageBox.confirm(
    t('Approval.ReviewGroup.confirmDelete'),
    t('Approval.ReviewGroup.confirmTitle'),
    {
      confirmButtonText: t('Approval.ReviewGroup.confirm'),
      cancelButtonText: t('Approval.ReviewGroup.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    if (!deleteRow.value?.id) return
    try {
      await reviewGroupService.delete(deleteRow.value.id)
      ElMessage.success(t('Approval.ReviewGroup.deleteSuccess'))
      fetchData() // 刷新列表
    } catch (error) {
      ElMessage.error(t('Approval.ReviewGroup.deleteFailed'))
    }
  }).catch(() => {
  })
}

// 复制审核组
const handleCopy = async (row: IApprovalReviewGroup) => {
  try {
    await reviewGroupService.copy(row.id!)
    ElMessage.success(t('Approval.ReviewGroup.copySuccess'))
    fetchData() // 刷新列表
  } catch (error) {
    ElMessage.error(t('Approval.ReviewGroup.copyFailed'))
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await reviewGroupService.getList(searchParams)
    if (data.code === 200 && data.data) {
      tableData.value = data.data.items
      pagination.total = data.data.total
    }
  } catch (error) {
    ElMessage.error(t('Approval.ReviewGroup.getListFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.bwms-module {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  
  .module-header {
    flex-shrink: 0; // 防止头部被压缩
  }
  
  .module-con {
    flex: 1; // 让内容区域占据剩余空间
    overflow: hidden; // 防止溢出
    display: flex;
    flex-direction: column;
    
    .box {
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      flex: 1; // 让盒子占据剩余空间
      display: flex;
      flex-direction: column;
      overflow: auto; // 改为 auto，允许整个内容区域滚动

      .el-table {
        margin-bottom: 20px; // 添加底部边距，与分页保持间隔
      }

      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        flex-shrink: 0; // 防止分页区域被压缩
      }
    }
  }
}
</style> 
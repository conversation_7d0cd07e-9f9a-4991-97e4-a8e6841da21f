# Jenkins 部署配置文档

## 项目概述

BWMS 是一个基于 Laravel + Vue.js 的后台管理系统，包含：
- 后端：Laravel 11.x + PHP 8.2
- 前端：Vue 3 + TypeScript + Vite
- 数据库：MySQL
- 容器化：Docker + Docker Compose

## 环境要求

### Jenkins 服务器环境
- Jenkins 2.400+
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (用于前端构建)
- PHP 8.2+ (可选，用于代码检查)
- Git

### 必需的 Jenkins 插件
```
- Git Plugin
- Docker Pipeline Plugin
- NodeJS Plugin
- SSH Agent Plugin
- Publish Over SSH Plugin
- Pipeline Stage View Plugin
- Blue Ocean (可选，更好的界面)
```

## Jenkins 流水线配置

### 1. 创建 Jenkinsfile

在项目根目录创建 `Jenkinsfile`：

```groovy
pipeline {
    agent any
    
    environment {
        // 项目配置
        PROJECT_NAME = 'bwms'
        DOCKER_IMAGE = 'bwms-app'
        DOCKER_TAG = "${BUILD_NUMBER}"
        
        // 部署服务器配置
        DEPLOY_SERVER = 'your-deploy-server.com'
        DEPLOY_USER = 'deploy'
        DEPLOY_PATH = '/var/www/bwms'
        
        // Node.js 版本
        NODE_VERSION = '18'
    }
    
    tools {
        nodejs "${NODE_VERSION}"
    }
    
    stages {
        stage('代码检出') {
            steps {
                echo '正在检出代码...'
                checkout scm
                
                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                }
            }
        }
        
        stage('环境准备') {
            parallel {
                stage('后端依赖') {
                    steps {
                        echo '安装 Composer 依赖...'
                        sh '''
                            if [ ! -f composer.phar ]; then
                                curl -sS https://getcomposer.org/installer | php
                            fi
                            php composer.phar install --no-dev --optimize-autoloader --no-interaction
                        '''
                    }
                }
                
                stage('前端依赖') {
                    steps {
                        echo '安装前端依赖...'
                        sh '''
                            node --version
                            npm --version
                            
                            # 清理缓存
                            npm cache clean --force
                            
                            # 安装依赖
                            npm install --production=false
                            
                            # 检查模块依赖
                            if [ -f "./prod.sh" ]; then
                                chmod +x ./prod.sh
                                ./prod.sh modules
                            fi
                        '''
                    }
                }
            }
        }
        
        stage('代码质量检查') {
            parallel {
                stage('PHP 代码检查') {
                    steps {
                        echo '执行 PHP 代码质量检查...'
                        sh '''
                            # PHP 语法检查
                            find . -name "*.php" -not -path "./vendor/*" -not -path "./node_modules/*" | xargs -I {} php -l {}
                            
                            # 代码格式检查 (如果有 PHP CS Fixer)
                            if [ -f vendor/bin/php-cs-fixer ]; then
                                vendor/bin/php-cs-fixer fix --dry-run --diff
                            fi
                        '''
                    }
                }
                
                stage('前端代码检查') {
                    steps {
                        echo '执行前端代码质量检查...'
                        sh '''
                            # TypeScript 类型检查
                            npx vue-tsc --noEmit
                            
                            # ESLint 检查 (如果配置了)
                            if [ -f .eslintrc.js ] || [ -f .eslintrc.json ]; then
                                npm run lint || true
                            fi
                        '''
                    }
                }
            }
        }
        
        stage('构建') {
            parallel {
                stage('前端构建') {
                    steps {
                        echo '构建前端资源...'
                        sh '''
                            # 设置构建环境
                            export NODE_OPTIONS="--max-old-space-size=4096"
                            
                            # 根据分支选择构建模式
                            if [ "${BRANCH_NAME}" = "master" ] || [ "${BRANCH_NAME}" = "main" ]; then
                                echo "生产环境构建"
                                npm run build:prod
                            elif [ "${BRANCH_NAME}" = "test" ]; then
                                echo "测试环境构建"
                                npm run build:test
                            else
                                echo "开发环境构建"
                                npm run build
                            fi
                            
                            # 检查构建结果
                            if [ ! -d "public/build" ]; then
                                echo "前端构建失败：找不到构建输出目录"
                                exit 1
                            fi
                            
                            echo "前端构建完成，文件列表："
                            ls -la public/build/
                        '''
                    }
                }
                
                stage('后端配置') {
                    steps {
                        echo '准备后端配置...'
                        sh '''
                            # 复制环境配置
                            if [ ! -f .env ]; then
                                cp .env.example .env
                            fi
                            
                            # 生成应用密钥 (如果需要)
                            if ! grep -q "APP_KEY=" .env || [ -z "$(grep APP_KEY= .env | cut -d'=' -f2)" ]; then
                                php artisan key:generate --no-interaction
                            fi
                            
                            # 清理缓存
                            php artisan config:clear
                            php artisan cache:clear
                            php artisan view:clear
                        '''
                    }
                }
            }
        }
        
        stage('测试') {
            steps {
                echo '执行测试...'
                sh '''
                    # 运行 PHP 测试 (如果有)
                    if [ -f vendor/bin/pest ]; then
                        vendor/bin/pest --parallel
                    elif [ -f vendor/bin/phpunit ]; then
                        vendor/bin/phpunit
                    fi
                    
                    # 运行前端测试 (如果有)
                    if grep -q "test" package.json; then
                        npm test || true
                    fi
                '''
            }
        }
        
        stage('构建 Docker 镜像') {
            when {
                anyOf {
                    branch 'master'
                    branch 'main'
                    branch 'test'
                    branch 'develop'
                }
            }
            steps {
                echo '构建 Docker 镜像...'
                script {
                    def image = docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}")
                    docker.withRegistry('', 'docker-registry-credentials') {
                        image.push()
                        image.push('latest')
                    }
                }
            }
        }
        
        stage('部署') {
            when {
                anyOf {
                    branch 'master'
                    branch 'main'
                }
            }
            steps {
                echo '部署到生产环境...'
                sshagent(['deploy-ssh-key']) {
                    sh '''
                        # 创建部署包
                        tar -czf bwms-${BUILD_NUMBER}.tar.gz \
                            --exclude=node_modules \
                            --exclude=.git \
                            --exclude=storage/logs/* \
                            --exclude=storage/framework/cache/* \
                            --exclude=storage/framework/sessions/* \
                            --exclude=storage/framework/views/* \
                            .
                        
                        # 上传到服务器
                        scp bwms-${BUILD_NUMBER}.tar.gz ${DEPLOY_USER}@${DEPLOY_SERVER}:/tmp/
                        
                        # 执行部署脚本
                        ssh ${DEPLOY_USER}@${DEPLOY_SERVER} "
                            cd ${DEPLOY_PATH}
                            
                            # 备份当前版本
                            if [ -d current ]; then
                                mv current backup-\$(date +%Y%m%d-%H%M%S)
                            fi
                            
                            # 解压新版本
                            mkdir -p current
                            cd current
                            tar -xzf /tmp/bwms-${BUILD_NUMBER}.tar.gz
                            
                            # 设置权限
                            chmod -R 755 storage bootstrap/cache
                            chown -R www-data:www-data storage bootstrap/cache
                            
                            # 创建符号链接
                            ln -sf \${PWD}/storage/app/public \${PWD}/public/storage
                            
                            # 运行迁移 (谨慎使用)
                            # php artisan migrate --force
                            
                            # 清理缓存
                            php artisan config:cache
                            php artisan route:cache
                            php artisan view:cache
                            
                            # 重启服务 (根据实际情况调整)
                            sudo systemctl reload nginx
                            sudo systemctl restart php8.2-fpm
                            
                            # 清理临时文件
                            rm -f /tmp/bwms-${BUILD_NUMBER}.tar.gz
                        "
                    '''
                }
            }
        }
    }
    
    post {
        always {
            echo '清理工作空间...'
            sh '''
                # 清理构建文件
                rm -f *.tar.gz
                
                # 清理 node_modules (可选)
                # rm -rf node_modules
            '''
        }
        
        success {
            echo '部署成功！'
            // 可以添加成功通知
        }
        
        failure {
            echo '部署失败！'
            // 可以添加失败通知
        }
    }
}
```

### 2. Dockerfile 配置

创建 `Dockerfile`：

```dockerfile
FROM php:8.2-fpm-alpine

# 安装系统依赖
RUN apk add --no-cache \
    nginx \
    supervisor \
    curl \
    zip \
    unzip \
    git \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libzip-dev \
    icu-dev \
    oniguruma-dev

# 安装 PHP 扩展
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        gd \
        zip \
        intl \
        mbstring \
        exif \
        bcmath

# 安装 Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 设置工作目录
WORKDIR /var/www/html

# 复制项目文件
COPY . .

# 安装依赖
RUN composer install --no-dev --optimize-autoloader --no-interaction

# 设置权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 storage bootstrap/cache

# 复制配置文件
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 暴露端口
EXPOSE 80

# 启动命令
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

## Jenkins 任务配置步骤

### 1. 创建新的流水线任务

1. 登录 Jenkins 管理界面
2. 点击 "新建任务"
3. 输入任务名称：`bwms-deployment`
4. 选择 "流水线" 类型
5. 点击 "确定"

### 2. 配置流水线

#### 基本配置
- **描述**: BWMS 项目自动化部署流水线
- **丢弃旧的构建**: 保留最近 10 次构建

#### 构建触发器
- ✅ **GitHub hook trigger for GITScm polling** (如果使用 GitHub)
- ✅ **轮询 SCM**: `H/5 * * * *` (每5分钟检查一次)

#### 流水线配置
- **定义**: Pipeline script from SCM
- **SCM**: Git
- **仓库 URL**: `https://github.com/your-username/bwms.git`
- **凭据**: 选择 Git 凭据
- **分支**: `*/main` 或 `*/master`
- **脚本路径**: `Jenkinsfile`

### 3. 配置凭据

在 Jenkins 管理 → 凭据管理中添加：

1. **Git 凭据** (用户名密码或 SSH 密钥)
2. **Docker Registry 凭据** (如果使用私有镜像仓库)
3. **SSH 部署密钥** (用于部署到服务器)

### 4. 安装必需的插件

```bash
# 在 Jenkins 管理 → 插件管理中安装
- Git Plugin
- Pipeline Plugin
- Docker Pipeline Plugin
- NodeJS Plugin
- SSH Agent Plugin
- Publish Over SSH Plugin
```

### 5. 配置 Node.js

1. 进入 Jenkins 管理 → 全局工具配置
2. 找到 NodeJS 配置
3. 添加 NodeJS 安装：
   - 名称: `18`
   - 版本: `NodeJS 18.x`
   - 自动安装: ✅

## 部署服务器配置

### 1. 服务器环境准备

```bash
# 安装必需软件
sudo apt update
sudo apt install -y nginx php8.2-fpm mysql-server docker.io docker-compose

# 创建部署目录
sudo mkdir -p /var/www/bwms
sudo chown deploy:deploy /var/www/bwms

# 配置 SSH 密钥
mkdir -p ~/.ssh
# 将 Jenkins 的公钥添加到 ~/.ssh/authorized_keys
```

### 2. Nginx 配置

创建 `/etc/nginx/sites-available/bwms`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/bwms/current/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/bwms /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 使用说明

### 1. 触发构建

- **自动触发**: 推送代码到 main/master 分支
- **手动触发**: 在 Jenkins 界面点击 "立即构建"

### 2. 监控构建

- 在 Jenkins 界面查看构建日志
- 使用 Blue Ocean 插件获得更好的可视化体验

### 3. 回滚操作

如果部署出现问题，可以快速回滚：

```bash
ssh <EMAIL>
cd /var/www/bwms
rm -rf current
mv backup-YYYYMMDD-HHMMSS current
sudo systemctl reload nginx
```

## 故障排除

### 常见问题

1. **前端构建内存不足**
   ```bash
   export NODE_OPTIONS="--max-old-space-size=4096"
   ```

2. **权限问题**
   ```bash
   sudo chown -R www-data:www-data storage bootstrap/cache
   chmod -R 755 storage bootstrap/cache
   ```

3. **数据库连接问题**
   - 检查 `.env` 文件配置
   - 确认数据库服务运行状态

### 日志查看

- **Jenkins 构建日志**: Jenkins 界面 → 构建历史 → 控制台输出
- **应用日志**: `/var/www/bwms/current/storage/logs/laravel.log`
- **Nginx 日志**: `/var/log/nginx/error.log`
- **PHP-FPM 日志**: `/var/log/php8.2-fpm.log`

## 安全建议

1. 使用 HTTPS 部署
2. 定期更新依赖包
3. 配置防火墙规则
4. 使用专用的部署用户
5. 定期备份数据库和代码

---

**注意**: 请根据实际环境调整配置参数，特别是服务器地址、数据库配置和域名等信息。
